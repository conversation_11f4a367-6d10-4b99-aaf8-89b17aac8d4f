import re
import csv
import logging
from typing import Any, Dict, List, Literal, Optional, Union, AsyncGenerator, Set, Tuple
import asyncio
import hashlib
import json
import time
import requests
import aiohttp

import litellm
from litellm._logging import verbose_proxy_logger as litellm_logger
from litellm.caching.caching import DualCache
from litellm.integrations.custom_guardrail import CustomGuardrail
from litellm.proxy._types import User<PERSON><PERSON><PERSON>eyAuth
from litellm.proxy.guardrails.guardrail_helpers import should_proceed_based_on_metadata
from litellm.types.guardrails import GuardrailEventHooks
from litellm.types.utils import ModelResponseStream

# 创建自定义logger
logger = logging.getLogger("sensitive_word_guardrail")
logger.setLevel(logging.DEBUG)
formatter = logging.Formatter(fmt='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
                             datefmt='%m/%d/%Y %H:%M:%S')
# 控制台handler
console = logging.StreamHandler()
console.setLevel(logging.INFO)
console.setFormatter(formatter)
# 确保不会重复添加handler
if not logger.handlers:
    logger.addHandler(console)


class HybridModerationGuardrail(CustomGuardrail):
    # 敏感词类型映射
    SENSITIVE_TYPE_MAP = {
        "0": "不做划分",
        "1": "煽动颠覆国家政权、推翻社会主义制度",
        "2": "危害国家安全和利益、损害国家形象",
        "3": "煽动分裂国家、破坏国家统一和社会稳定",
        "4": "宣扬恐怖主义、极端主义",
        "5": "宣扬民族仇恨",
        "6": "宣扬暴力、淫秽色情",
        "7": "传播虚假有害信息",
        "8": "其他法律、行政法规禁止的内容",
        "9": "民族歧视内容",
        "10": "信仰歧视内容",
        "11": "国别歧视内容",
        "12": "地域歧视内容",
        "13": "性别歧视内容",
        "14": "年龄歧视内容",
        "15": "职业歧视内容",
        "16": "健康歧视内容",
        "17": "其他方面歧视内容",
        "18": "侵犯他人其他合法权益",
        "19": "侵害他人个人信息权益",
        "20": "侵害他人名誉权",
        "21": "侵害他人荣誉权",
        "22": "侵害他人肖像权",
        "23": "侵害他人隐私权",
        "24": "危害他人身心健康",
        "25": "利用算法、数据、平台等优势，实施垄断和不正当竞争行为",
        "26": "其他商业违法违规行为",
        "27": "侵犯他人知识产权",
        "28": "违反商业道德",
        "29": "泄露他人商业秘密"
    }

    # 常见的正则表达式特殊字符
    REGEX_SPECIAL_CHARS = set('[](){}*+?.\\|^$')

    # LLM Moderation constants (similar to LLMModerationGuardrail)
    DEFAULT_PROMPT_TEMPLATE = """你是一个内容审核助手。你的任务是判断以下内容是否包含有害、不当或违反规定的内容。
    
内容: """

    DEFAULT_MODERATION_CATEGORIES = [
        "危害",
        "仇恨言论",
        "骚扰",
        "自残",
        "性内容",
        "暴力内容",
        "未成年人虐待",
        "违法活动",
        "欺诈或欺骗",
        "滥用个人信息",
        "政治敏感"
    ]

    MODIFIED_DEFAULT_SUFFIX = """

请分析上述内容是否属于以下任何类别 (请严格按顺序编号):
{numbered_categories}

请以JSON格式回答，包含以下字段:
1. "violated_indices": [数字列表] - 列出所有触发的类别对应的从0开始的序号。如果没有，则为空列表。

例如，如果内容同时违反了列表中的第1项 (序号0) 和第3项 (序号2) 类别，则返回：
{{
  "violated_indices": [0, 2]
}}
如果内容未违反任何规定，则返回：
{{
  "violated_indices": []
}}

重要提示：您的回答必须严格遵守以下规则：
1. 必须仅包含一个标准的、有效的JSON对象。
2. JSON对象是您回答的唯一内容。不要包含任何markdown标记（例如 ```json ... ``` 或 ```）、任何解释性文字、任何前缀或后缀，或任何其他非JSON字符。
3. 直接输出JSON对象。
"""

    DEFAULT_SUFFIX = """

请分析上述内容是否属于以下任何类别: 
{categories}

请以JSON格式回答，包含以下字段:
1. "violates_policy": true/false - 内容是否违反规定
2. "categories": 列出触发的所有类别，没有则为空列表
3. "reason": 简要解释判断原因

仅返回JSON格式结果，不包含其他文本。
"""

    def __init__(
        self,
        # SecLLM parameters (now primary for LLM moderation)
        secllm_model: str = None, 
        secllm_api_base: str = None,
        secllm_api_key: str = None,
        sensitive_words: List[str] = None,
        case_sensitive: bool = False,
        use_regex: bool = False,
        delay_chunks: int = 10,
        sensitive_words_file: Optional[str] = None,
        sensitive_words_csv: Optional[str] = None, 
        encoding: str = "utf-8",
        default_on: bool = False, 
        auto_detect_regex: bool = False,
        prompt_template: Optional[str] = None,
        categories: Optional[List[str]] = None,
        cache_results: bool = True,
        cache_timeout: int = 3600,
        **kwargs,
    ):
        """
        基于敏感词表和LLM API的混合过滤围栏 (仅使用SecLLM进行审核)
        
        Args:
            secllm_model: SecLLM model name (e.g., "secllm-v2")
            secllm_api_base: SecLLM API base URL (e.g., "http://10.5.225.21:11035/v1")
            secllm_api_key: SecLLM API key (e.g., "sk-owl-secllm")
            sensitive_words: 敏感词列表
            case_sensitive: 是否区分大小写
            use_regex: 是否将敏感词作为正则表达式处理
            delay_chunks: 流式输出时延迟处理的chunk数量，用于检测跨chunk的敏感词
            sensitive_words_file: 敏感词文件路径
            sensitive_words_csv: 敏感词CSV文件路径，格式为"敏感词,类型编号"
            encoding: 敏感词文件编码
            default_on: 此 Guardrail 是否默认启用。默认为 False。
            auto_detect_regex: 自动检测并处理正则表达式格式的敏感词
            # LLM Params
            prompt_template: 用于审核的提示模板
            categories: 需要检查的违规类别列表
            cache_results: 是否缓存LLM审核结果
            cache_timeout: LLM审核结果缓存有效期（秒）
        """
        logger.info("开始初始化混合审核围栏 (仅支持SecLLM)...")
        
        self.sensitive_words_dict = {}
        
        default_sensitive_words = {
            "海洛因": "6", "冰毒": "6", "摇头丸": "6", "大麻": "6", 
            "可卡因": "6", "鸦片": "6", "吗啡": "6"
        }
        
        if sensitive_words:
            for word in sensitive_words:
                self.sensitive_words_dict[word] = ("0", False)
        
        for word, type_code in default_sensitive_words.items():
            self.sensitive_words_dict[word] = (type_code, False)
        
        if not sensitive_words_csv:
            # sensitive_words_csv = "/home/<USER>/litellm/sensitive_words_raw.csv" # 注释掉硬编码路径
            logger.info(f"未提供 sensitive_words_csv 参数，将不加载CSV敏感词列表。如需加载，请指定路径。")
        
        self.case_sensitive = case_sensitive
        self.use_regex = use_regex
        self.auto_detect_regex = auto_detect_regex
        self.delay_chunks = max(1, delay_chunks)
        self.compiled_patterns = []
        
        self.optional_params = kwargs
        
        self._load_sensitive_words(sensitive_words_file, encoding)
        self._load_csv_sensitive_words(sensitive_words_csv, encoding)
        
        self.sensitive_words = set(self.sensitive_words_dict.keys())
        
        if self.sensitive_words:
            self._compile_patterns()
            logger.info(f"已编译 {len(self.compiled_patterns)} 个敏感词模式，共包含 {len(self.sensitive_words_dict)} 个有效敏感词。")
        else:
            logger.warning("敏感词列表为空，围栏将不会阻止任何内容。")
        
        self.default_on = default_on
        self.guardrail_name = kwargs.get("guardrail_name", "hybrid_moderation_guardrail")

        # Initialize LLM Moderation settings
        self.prompt_template = prompt_template or self.DEFAULT_PROMPT_TEMPLATE
        self.categories = categories or self.DEFAULT_MODERATION_CATEGORIES
        self.cache_results = cache_results
        self.cache_timeout = cache_timeout
        self.cache: Dict[str, Dict[str, Any]] = {} 

        # Store SecLLM specific parameters
        self.secllm_model = secllm_model if secllm_model else "qwen3"
        self.secllm_api_base = secllm_api_base if secllm_api_base else "http://10.24.107.199:11055/v1"
        self.secllm_api_key = secllm_api_key if secllm_api_key else "sk-owl-secllm"

        if not (self.secllm_model and self.secllm_api_base and self.secllm_api_key):
            # This case should ideally not happen if params are non-optional and type-hinted as str
            # However, if they could somehow be passed as None or empty, this log is useful.
            logger.error("SecLLM配置 (secllm_model, secllm_api_base, secllm_api_key) 未完整提供或无效。LLM审核功能将不可用。")
            # Consider raising ValueError here if guardrail cannot function without them.
            # raise ValueError("SecLLM configuration is incomplete and required for moderation.")
        else:
            logger.info(f"使用SecLLM进行内容审核: model='{self.secllm_model}', api_base='{self.secllm_api_base}'")

        numbered_categories_str = "\n".join([f"{i}. {cat}" for i, cat in enumerate(self.categories)])
        self.suffix = self.MODIFIED_DEFAULT_SUFFIX.format(numbered_categories=numbered_categories_str)
        
        logger.info(f"混合审核围栏初始化完成: 名称='{self.guardrail_name}', 默认启用={self.default_on}, "
                    f"加载敏感词总数={len(self.sensitive_words_dict)}, 正则模式数={len(self.compiled_patterns)}, "
                    f"审核模型='SecLLM ({self.secllm_model})', LLM缓存={'启用' if self.cache_results else '禁用'}.")
        super().__init__(**kwargs)
    
    async def _make_secllm_request(self, prompt_content: str) -> str: # Returns raw response text
        """Makes an asynchronous request to the SecLLM API and returns the response text."""
        if not self.secllm_api_base or not self.secllm_api_key or not self.secllm_model:
            logger.error(f"SecLLM 配置不完整，无法发送请求。 API Base: {self.secllm_api_base}, Key: {'已设置' if self.secllm_api_key else '未设置'}, Model: {self.secllm_model}")
            raise ValueError("SecLLM configuration is incomplete.")

        headers = {
            "Authorization": f"Bearer {self.secllm_api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": self.secllm_model,
            "messages": [{"role": "user", "content": prompt_content}],
            "temperature": 0, 
            "max_tokens": 500,
            "stream": False,
            "top_p": 0.01
        }
        
        logger.debug(f"向 SecLLM 发送异步请求: API Base: {self.secllm_api_base}, Model: {self.secllm_model}")
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
            response_text_content = "" # Initialize in case of early error
            try:
                async with session.post(self.secllm_api_base, headers=headers, json=payload) as response:
                    response_text_content = await response.text() # Store text for potential error logging
                    response.raise_for_status()

                    response_json = await response.json() # Parse JSON after ensuring status is OK
                    if (response_json and response_json.get("choices") and 
                       isinstance(response_json["choices"], list) and len(response_json["choices"]) > 0 and 
                       response_json["choices"][0].get("message") and 
                       isinstance(response_json["choices"][0]["message"], dict) and 
                       response_json["choices"][0]["message"].get("content")):
                        return response_json["choices"][0]["message"]["content"].strip()
                    else:
                        logger.warning(f"SecLLM异步响应格式不符合预期: {response_json}")
                        raise ValueError(f"SecLLM returned unexpected JSON structure: {response_text_content[:500]}")

            except aiohttp.ClientResponseError as e:
                error_response_text = response_text_content
                if not error_response_text and hasattr(e, 'response') and e.response:
                    try:
                        error_response_text = await e.response.text()
                    except Exception:
                        error_response_text = str(e) # Fallback if .text() fails or not available
                elif not error_response_text:
                    error_response_text = str(e)
                
                logger.error(f"SecLLM 异步HTTP错误: {e.status} - {e.message} - {error_response_text[:500]}")
                if e.status == 401:
                    raise PermissionError(f"SecLLM authentication failed (401): {error_response_text[:500]}") from e
                elif e.status == 429:
                    raise ConnectionAbortedError(f"SecLLM rate limit hit (429): {error_response_text[:500]}") from e
                raise ConnectionError(f"SecLLM HTTP error {e.status}: {error_response_text[:500]}") from e
            except asyncio.TimeoutError as e:
                logger.error(f"SecLLM 异步请求超时: {e}")
                raise ConnectionError(f"SecLLM request timed out: {e}") from e
            except aiohttp.ClientError as e:
                logger.error(f"SecLLM 异步请求失败 (aiohttp.ClientError): {e}")
                raise ConnectionError(f"SecLLM request failed: {e}") from e
            except json.JSONDecodeError as e:
                logger.error(f"SecLLM 异步响应无法解析为JSON: {response_text_content[:500]}... Error: {e}")
                raise ValueError(f"SecLLM response was not valid JSON: {response_text_content[:500]}") from e

    def _is_regex_pattern(self, word: str) -> bool:
        """判断字符串是否可能是正则表达式模式"""
        # 简单判断是否包含正则特殊字符
        if any(c in self.REGEX_SPECIAL_CHARS for c in word):
            return True
        # 检查是否有转义序列
        if '\\' in word:
            return True
        # 检查是否有量词
        if re.search(r'\{\d+,?\d*\}', word):
            return True
        return False
    
    def _validate_regex(self, pattern: str) -> bool:
        """验证字符串是否为有效的正则表达式"""
        try:
            re.compile(pattern)
            return True
        except re.error:
            return False
    
    def _load_sensitive_words(self, file_path: Optional[str], encoding: str):
        """从文件加载敏感词列表"""
        if not file_path:
            logger.debug("未提供敏感词文件路径 (sensitive_words_file)，跳过加载")
            return

        try:
            word_count = 0
            with open(file_path, 'r', encoding=encoding) as f:
                for line in f:
                    word = line.strip()
                    if word:
                        # 检查是否是正则表达式格式
                        is_regex = False
                        if self.auto_detect_regex and self._is_regex_pattern(word):
                            if self._validate_regex(word):
                                is_regex = True
                                logger.debug(f"从文件 '{file_path}' 检测到正则表达式格式敏感词: {word}")
                        
                        self.sensitive_words_dict[word] = ("0", is_regex)  # 默认类型为0
                        word_count += 1
            logger.info(f"从文本文件 '{file_path}' 加载了 {word_count} 个敏感词 (默认类型'0')")
        except FileNotFoundError:
            logger.error(f"找不到敏感词文件: '{file_path}'")
        except (IOError, UnicodeDecodeError) as e:
            logger.error(f"读取敏感词文件 '{file_path}' 时发生错误: {e}")
    
    def _load_csv_sensitive_words(self, csv_path: Optional[str], encoding: str):
        """从CSV文件加载敏感词和类型"""
        if not csv_path:
            logger.debug("未提供CSV敏感词文件路径 (sensitive_words_csv)，跳过加载")
            return
        
        try:
            word_count = 0
            regex_count = 0
            skipped_type_zero_count = 0 # 记录因为类型为"0"而被跳过的词数
            with open(csv_path, 'r', encoding=encoding) as f:
                # 尝试自动检测CSV格式
                sample = f.read(1024)
                f.seek(0)
                
                # 检查分隔符
                try:
                    dialect = csv.Sniffer().sniff(sample)
                    reader = csv.reader(f, dialect)
                    logger.debug(f"CSV文件 '{csv_path}' 检测到分隔符: '{dialect.delimiter}', 引号字符: '{dialect.quotechar}'")
                except csv.Error:
                    logger.warning(f"无法自动检测CSV文件 '{csv_path}' 的方言，将使用默认逗号分隔符。如果解析失败，请确保文件格式正确。")
                    f.seek(0) # 重置文件指针
                    reader = csv.reader(f) # 使用默认逗号分隔
                
                # 跳过标题行
                header = next(reader, None)
                if not header or len(header) < 2:
                    logger.warning(f"CSV文件 '{csv_path}' 的标题行可能不正确或列数不足 (应至少包含'敏感词'和'类型编号')。标题行: {header}")
                else:
                    logger.debug(f"CSV文件 '{csv_path}' 的标题行: {header}")
                
                # 读取敏感词和类型
                for i, row in enumerate(reader, start=1): # 从1开始计数行号 (扣除标题)
                    if len(row) >= 2:
                        word = row[0].strip()
                        type_code = row[1].strip()
                        
                        if not word:
                            logger.debug(f"CSV文件 '{csv_path}' 第 {i+1} 行: 敏感词为空，已跳过。")
                            continue
                            
                        if type_code == "0":
                            skipped_type_zero_count += 1
                            # logger.debug(f"CSV文件 '{csv_path}' 第 {i+1} 行: 敏感词 '{word}' 类型为 '0'，已跳过。")
                            continue # 类型为"0"的词条不加载
                            
                        if word:
                            # 检查是否是正则表达式格式
                            is_regex = False
                            if self.auto_detect_regex and self._is_regex_pattern(word):
                                if self._validate_regex(word):
                                    is_regex = True
                                    regex_count += 1
                                    logger.debug(f"从CSV '{csv_path}' 检测到正则表达式格式敏感词: {word} (类型: {type_code})")
                                else:
                                    logger.warning(f"CSV文件 '{csv_path}' 中的敏感词 '{word}' (类型: {type_code}) 疑似正则表达式但无效，将作为普通文本处理。")
                            
                            self.sensitive_words_dict[word] = (type_code, is_regex)
                            word_count += 1
                            
                            # 每1000个词记录一次进度
                            if word_count % 1000 == 0:
                                logger.debug(f"已从CSV '{csv_path}' 加载 {word_count} 个敏感词...")
                    else:
                        logger.warning(f"CSV文件 '{csv_path}' 第 {i+1} 行格式不正确 (列数不足)，已跳过: {row}")
            
            logger.info(f"从CSV文件 '{csv_path}' 加载了 {word_count} 个敏感词 (其中 {regex_count} 个为正则表达式格式)。因类型为'0'而跳过 {skipped_type_zero_count} 个词。")
            
            # 记录敏感词类型分布
            type_counts = {}
            for word, (type_code, _) in self.sensitive_words_dict.items():
                type_counts[type_code] = type_counts.get(type_code, 0) + 1
            
            for type_code, count in sorted(type_counts.items()):
                type_desc = self.SENSITIVE_TYPE_MAP.get(type_code, "未知类型")
                logger.info(f"类型 {type_code} ({type_desc}): {count} 个敏感词")
                
        except FileNotFoundError:
            logger.error(f"找不到CSV敏感词文件: {csv_path}")
        except (IOError, UnicodeDecodeError, csv.Error) as e:
            logger.error(f"读取CSV敏感词文件 {csv_path} 错误: {e}")
    
    def _compile_patterns(self):
        """预编译敏感词的正则表达式。对正则表达式格式和普通文本分别处理。"""
        self.compiled_patterns = []
        if not self.sensitive_words:
            return

        logger.debug(f"开始编译敏感词正则表达式，共 {len(self.sensitive_words)} 个词")
        
        # 分离普通敏感词和正则表达式格式敏感词
        normal_words = []
        regex_patterns = []
        
        for word, (type_code, is_regex) in self.sensitive_words_dict.items():
            if is_regex or self.use_regex:
                regex_patterns.append((word, type_code))
            else:
                normal_words.append((word, type_code))
        
        logger.debug(f"普通敏感词: {len(normal_words)}个，正则表达式格式敏感词: {len(regex_patterns)}个")
        
        # 处理正则表达式格式敏感词
        for pattern, type_code in regex_patterns:
            try:
                if not self.case_sensitive:
                    regex = re.compile(pattern, re.IGNORECASE)
                else:
                    regex = re.compile(pattern)
                # 存储编译后的正则表达式、原始模式字符串和类型
                self.compiled_patterns.append((regex, pattern, type_code))
                logger.debug(f"编译正则表达式敏感词: {pattern}，类型: {type_code}")
            except re.error as e:
                logger.warning(f"跳过无效的正则表达式模式 '{pattern}': {e}")
        
        # 处理普通敏感词，分批次创建合并模式
        if normal_words:
            try:
                # 如果敏感词数量过多，分批处理以避免正则表达式过长
                batch_size = 5000  # 每批次处理的敏感词数量
                word_batches = [normal_words[i:i+batch_size] for i in range(0, len(normal_words), batch_size)]
                
                for batch_idx, words_batch in enumerate(word_batches):
                    # pattern_to_original: maps a lookup key to (original_word, type_code)
                    # patterns_for_regex_str: list of re.escape(original_word)
                    pattern_to_original = {}
                    patterns_for_regex_str = []
                    
                    for word, type_code in words_batch:
                        patterns_for_regex_str.append(re.escape(word))
                        if not self.case_sensitive:
                            # For case-insensitive, map the lowercased word to original
                            # If multiple original words map to the same lowercased word,
                            # the first one encountered takes precedence.
                            lower_word = word.lower()
                            if lower_word not in pattern_to_original:
                                pattern_to_original[lower_word] = (word, type_code)
                        else: # case-sensitive
                            pattern_to_original[word] = (word, type_code)
                    
                    pattern_string = "|".join(patterns_for_regex_str)
                    flags = re.IGNORECASE if not self.case_sensitive else 0
                    combined_regex = re.compile(pattern_string, flags)
                    
                    # 存储编译后的正则表达式、映射和批次索引
                    self.compiled_patterns.append((combined_regex, pattern_to_original, f"batch_{batch_idx}"))
                    logger.debug(f"完成普通敏感词批次 {batch_idx+1}/{len(word_batches)}，包含 {len(words_batch)} 个词，映射表大小: {len(pattern_to_original)}")
                
                logger.info(f"已创建 {len(self.compiled_patterns)} 个正则表达式模式，覆盖所有 {len(self.sensitive_words)} 个敏感词")
            except re.error as e:
                logger.error(f"编译敏感词组合模式时出错: {e}")
            except Exception as e: 
                logger.error(f"编译敏感词组合模式时发生意外错误: {e}")
    
    def _hash_content(self, content: str) -> str:
        """为内容创建哈希值用于缓存"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def _is_cached_result_valid(self, hash_key: str) -> bool:
        """检查缓存结果是否有效（未过期）"""
        if not self.cache_results or hash_key not in self.cache:
            return False
        
        cached_time = self.cache[hash_key].get("timestamp", 0)
        return (time.time() - cached_time) < self.cache_timeout

    async def _check_content_with_llm(self, content: str) -> Tuple[bool, Dict]:
        """
        使用SecLLM API检查内容是否违规
        """
        if not content or not isinstance(content, str):
            return False, {"violates_policy": False, "violated_indices": [], "categories": []}
        
        # Content truncation (as before)
        max_content_len = 8000 
        if len(content) > max_content_len:
            content_for_llm = content[:max_content_len] + "...[内容被截断]"
        else:
            content_for_llm = content
        
        # Cache check (as before)
        content_hash = self._hash_content(content_for_llm)
        if self._is_cached_result_valid(content_hash):
            cached_result = self.cache[content_hash]
            return cached_result["violates_policy"], cached_result
        
        logger.debug(f"混合审核围栏 [{self.guardrail_name}] LLM检查: 内容哈希 {content_hash}，未命中缓存。调用模型 {self.secllm_model} 进行审核。内容预览 (前100字符): '{content_for_llm[:100]}'...")
        
        response_text = ""
        try:
            prompt = self.prompt_template + content_for_llm + self.suffix
            response_text = await self._make_secllm_request(prompt)

            # JSON parsing logic (as before, now only for SecLLM response)
            match = re.search(r"```json\\n(.*?)\\n```", response_text, re.DOTALL)
            if match:
                json_str = match.group(1)
            else:
                json_str = response_text
            
            result = json.loads(json_str)
            violated_indices = result.get("violated_indices", [])
            violates_policy = bool(violated_indices)
            
            categories_found_names = []
            if isinstance(violated_indices, list):
                categories_found_names = [self.categories[i] for i in violated_indices if isinstance(i, int) and 0 <= i < len(self.categories)]
            else:
                logger.warning(f"混合审核围栏 [{self.guardrail_name}] LLM检查: 'violated_indices' 不是列表: {violated_indices}")
                violates_policy = False 

            logger.debug(f"混合审核围栏 [{self.guardrail_name}] LLM检查: 模型 {self.secllm_model} 返回结果。策略：{'违规' if violates_policy else '通过'}, 触发序号: {violated_indices}, 对应类别: {categories_found_names}")
            
            llm_response_data = {
                "violates_policy": violates_policy,
                "violated_indices": violated_indices,
                "categories": categories_found_names, 
                "timestamp": time.time()
            }

            if self.cache_results:
                self.cache[content_hash] = llm_response_data
            
            return violates_policy, llm_response_data
        
        except (ConnectionError, PermissionError, ConnectionAbortedError, ValueError) as e: 
            logger.error(f"混合审核围栏 [{self.guardrail_name}] LLM审核 请求或解析失败: {type(e).__name__} - {str(e)}")
            return False, {"violates_policy": False, "violated_indices": [], "categories": []}
        except json.JSONDecodeError as e: 
            logger.warning(f"混合审核围栏 [{self.guardrail_name}] LLM检查: 模型返回了非JSON格式响应 (或JSON解析失败): {response_text[:200]}... Error: {e}")
            return False, {"violates_policy": False, "violated_indices": [], "categories": []}
        except Exception as e: 
            logger.error(f"混合审核围栏 [{self.guardrail_name}] LLM审核 请求过程中发生意外错误: {type(e).__name__} - {str(e)}", exc_info=True)
            return False, {"violates_policy": False, "violated_indices": [], "categories": []}

    def _contains_sensitive_word(self, text: str) -> Tuple[bool, Dict[str, str]]:
        """
        快速检查文本中是否包含敏感词，一旦发现任何敏感词立即返回
        
        Returns:
            Tuple[bool, Dict[str, str]]: (是否包含敏感词, 找到的第一个敏感词及其类型)
        """
        if not text or not isinstance(text, str) or not self.compiled_patterns:
            return False, {}
        
        # 遍历所有编译好的模式
        for pattern_info in self.compiled_patterns:
            compiled_regex = pattern_info[0]
            pattern_data = pattern_info[1]  # For regex: original pattern string. For batch: pattern_to_original map.
            type_or_batch_id = pattern_info[2] # For regex: type_code. For batch: batch_id string.
            
            try:
                # 处理正则表达式格式敏感词 (pattern_data is the original regex string)
                if isinstance(pattern_data, str):
                    original_regex_str = pattern_data
                    type_code = type_or_batch_id # This is the type_code for this regex
                    match = compiled_regex.search(text)
                    if match:
                        logger.debug(f"文本中检测到正则表达式敏感词: '{original_regex_str}' (类型: {type_code}), 匹配内容: '{match.group(0)}'")
                        return True, {original_regex_str: type_code}
                
                # 处理普通敏感词批次 (pattern_data is the pattern_to_original map)
                elif isinstance(pattern_data, dict):
                    batch_map = pattern_data
                    match = compiled_regex.search(text)
                    if match:
                        match_text = match.group(0) # Actual text matched by one of the re.escaped words
                        
                        found_word_info = None
                        if not self.case_sensitive:
                            found_word_info = batch_map.get(match_text.lower())
                        else: # case-sensitive
                            found_word_info = batch_map.get(match_text)
                        
                        if found_word_info:
                            original_word, type_code = found_word_info
                            logger.debug(f"文本中检测到普通敏感词: '{original_word}' (类型: {type_code}), 来源于批次 '{type_or_batch_id}', 匹配内容: '{match_text}'")
                            return True, {original_word: type_code}
                        else:
                            # This case should ideally not be reached if the map is built correctly
                            # and match_text is one of the words from the batch.
                            # Could log a warning if this fallback is ever needed or if found_word_info is None.
                            logger.warning(f"组合正则匹配到 '{match_text}' 但无法从批次 '{type_or_batch_id}' 的映射表中找到对应原始词。请检查大小写敏感设置和映射构建逻辑。")
                            # To maintain safety, consider the match from compiled_regex as a generic hit for this batch if specific word cannot be retrieved.
                            # However, this would mean we can't return the specific word and type.
                            # For now, if direct map lookup fails, we assume it's a non-match for a *specific* word,
                            # although the combined regex did fire. This might need refinement if problematic.
                            # The previous version had a loop, which was more robust but complex.
                            # The goal here is simplification. If this 'else' path is hit, it's an issue to investigate.


            except Exception as e:
                logger.warning(f"快速正则表达式查找过程中出错: {e}")
        
        return False, {}
    
    def _format_error_message(self, found_words: Dict[str, str]) -> str:
        """格式化错误信息，包含敏感词及其类型"""
        if not found_words:
            logger.warning("调用 _format_error_message 时 found_words 为空，将返回通用错误消息。")
            return "输出内容包含敏感词"
        
        details = []
        for word, type_code in found_words.items():
            type_desc = self.SENSITIVE_TYPE_MAP.get(type_code, "未知类型")
            details.append(f"'{word}'[类型:{type_code}-{type_desc}]")
        
        return f"输出内容包含敏感词: {', '.join(details)}"
    
    async def async_pre_call_hook(
        self,
        user_api_key_dict: UserAPIKeyAuth,
        cache: DualCache,
        data: dict,
        call_type: Literal[
            "completion",
            "text_completion",
            "embeddings",
            "image_generation",
            "moderation",
            "audio_transcription",
            "pass_through_endpoint",
            "rerank"
        ],
    ) -> Optional[Union[Exception, str, dict]]:
        """
        在LLM API调用前运行。根据用户要求，此版本跳过输入内容的LLM检查。
        """
        # logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 调用前置钩子 (pre_call_hook) (类型: {call_type})。LLM输入检查已禁用，将直接返回原始数据。")
        
        # # Skip moderation for certain call types or if guardrail is not active
        # if call_type in ["image_generation", "audio_transcription", "embeddings", "moderation"]:
        #     logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 跳过对 {call_type} 类型的LLM输入审核。")
        #     return data

        # content_to_check: Optional[str] = None
        # content_source: str = ""

        # # 处理聊天消息
        # _messages = data.get("messages")
        # if _messages and isinstance(_messages, list):
        #     for i, message in reversed(list(enumerate(_messages))):
        #         if isinstance(message, dict) and message.get("role") == "user":
        #             _content = message.get("content")
        #             if isinstance(_content, str) and _content.strip():
        #                 content_to_check = _content
        #                 content_source = f"输入消息 {i+1}"
        #                 break
        
        # # 处理文本补全的prompt (如果聊天消息中未找到内容)
        # if not content_to_check:
        #     _prompt = data.get("prompt")
        #     if isinstance(_prompt, str) and _prompt.strip():
        #         content_to_check = _prompt
        #         content_source = "输入prompt"

        # if content_to_check:
        #     logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 提取到待LLM审核的内容 ({content_source})。预览 (前100字符): '{content_to_check[:100]}'...")
        #     violates_policy, result_dict = await self._check_content_with_llm(content_to_check)
            
        #     if violates_policy:
        #         categories = ", ".join(result_dict.get("categories", []))
        #         error_message = f"LLM审核拦截用户输入 ({content_source}): 内容策略违规。类别: [{categories}]"
        #         logger.warning(f"混合审核围栏 [{self.guardrail_name}]: {error_message}")
        #         raise ValueError(error_message)
        # else:
        #     logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 未在输入数据中找到需要LLM审核的文本内容。")
        
        # logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 前置调用LLM检查通过。")
        return data
    
    async def async_moderation_hook(
        self,
        data: dict,
        user_api_key_dict: UserAPIKeyAuth,
        call_type: Literal["completion", "embeddings", "image_generation", "moderation", "audio_transcription"],
    ):
        """
        并行于LLM API调用运行，检查输入是否包含敏感词
        """
        # logger.debug(f"敏感词围栏 [{self.guardrail_name}]: moderation_hook (类型: {call_type}) - 检查已禁用。")
        
        # # 使用快速检测方法
        # _messages = data.get("messages")
        # if _messages:
        #     for i, message in enumerate(_messages):
        #         _content = message.get("content")
        #         if isinstance(_content, str):
        #             has_sensitive, found_words = self._contains_sensitive_word(_content)
        #             if has_sensitive:
        #                 logger.warning(f"敏感词围栏 [{self.guardrail_name}] (Moderation): 在输入消息 {i+1} 中发现敏感词: {list(found_words.keys())}")
        #                 error_message = self._format_error_message(found_words)
        #                 raise ValueError(f"审查失败 (Moderation): {error_message}")
        
        # _prompt = data.get("prompt")
        # if isinstance(_prompt, str):
        #     has_sensitive, found_words = self._contains_sensitive_word(_prompt)
        #     if has_sensitive:
        #         logger.warning(f"敏感词围栏 [{self.guardrail_name}] (Moderation): 在输入prompt中发现敏感词: {list(found_words.keys())}")
        #         error_message = self._format_error_message(found_words)
        #         raise ValueError(f"审查失败 (Moderation): {error_message}")
        
        # logger.debug(f"敏感词围栏 [{self.guardrail_name}]: moderation_hook - 无操作执行。")
        pass
    
    async def async_post_call_success_hook(
        self,
        data: dict, # Original request data, not typically used for output check
        user_api_key_dict: UserAPIKeyAuth,
        response,
    ):
        """
        在LLM API调用成功后运行，检查输出是否包含违规内容 (使用LLM) - 非流式
        """
        logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 开始后置调用LLM成功检查 (post_call_success_hook) - 非流式输出")
        
        if isinstance(response, litellm.ModelResponse):
            for i, choice in enumerate(response.choices):
                content_to_check: Optional[str] = None
                if hasattr(choice, "message") and hasattr(choice.message, "content") and choice.message.content and isinstance(choice.message.content, str):
                    content_to_check = choice.message.content
                # Also handle text completion (e.g. for gpt-3.5-turbo-instruct) where content might be in choice.text
                elif hasattr(choice, "text") and choice.text and isinstance(choice.text, str):
                    content_to_check = choice.text # For text completion models
                
                if content_to_check:
                    logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 提取到待LLM审核的非流式输出 (选项 {i+1})。预览 (前100字符): '{content_to_check[:100]}'...")
                    violates_policy, result_dict = await self._check_content_with_llm(content_to_check)
                    
                    if violates_policy:
                        categories = ", ".join(result_dict.get("categories", []))
                        error_message = f"LLM审核拦截模型非流式输出 (选项 {i+1}): 内容策略违规。类别: [{categories}]"
                        logger.warning(f"混合审核围栏 [{self.guardrail_name}]: {error_message}")
                        # Create a new ModelResponse or modify existing one to reflect the error.
                        # For simplicity, raising an error that the proxy can catch and format.
                        raise ValueError(error_message) # Error will be caught by LiteLLM proxy
                    else:
                        logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 非流式输出 (选项 {i+1}) 通过LLM审核。")
                else:
                    logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 非流式输出选项 {i+1} 中无有效内容进行LLM审核。")
            logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 非流式输出LLM检查通过。")
        else:
            logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 跳过非ModelResponse/CompletionResponse类型的响应LLM检查: {type(response).__name__}")
        # This hook does not modify the response directly if it passes;
        # it raises an error if moderation fails.
        # So, we don't explicitly return the response here.
    
    async def async_post_call_streaming_iterator_hook(
        self,
        user_api_key_dict: UserAPIKeyAuth,
        response: Any, # This is an AsyncGenerator[ModelResponseStream, None]
        request_data: dict,
    ) -> AsyncGenerator[ModelResponseStream, None]:
        """
        处理流式输出，使用实时关键词检测，并在流结束后进行LLM全面检测。
        
        关键词检测提供快速的第一道防线。
        LLM检测在流结束后对完整内容进行更深入的上下文感知审核。
        """
        logger.debug(f"混合审核围栏 [{self.guardrail_name}]: 开始流式输出混合检查 (post_call_streaming_iterator_hook)")
        buffer = ""  # 累积接收到的内容
        chunks_queue: List[Tuple[Any, str]] = [] # 队列存储元组: (原始chunk对象, 该chunk的原始delta内容)
        queue_size = 0  # 追踪队列中的chunk数量
        chunk_counter = 0 # 追踪处理的chunk序号
        
        try:
            async for chunk in response:
                chunk_counter += 1
                delta_content = ""
                is_last_chunk = False
                
                if hasattr(chunk, "choices") and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    if hasattr(choice, "delta") and hasattr(choice.delta, "content") and choice.delta.content is not None:
                        delta_content = choice.delta.content
                        logger.debug(f"混合审核围栏 [{self.guardrail_name}] 流式 (Chunk {chunk_counter}): 收到delta: '{delta_content[:50]}...'")
                    if hasattr(choice, "finish_reason") and choice.finish_reason is not None:
                        is_last_chunk = True
                        logger.debug(f"混合审核围栏 [{self.guardrail_name}] 流式 (Chunk {chunk_counter}): 检测到最后一个chunk (finish_reason: {choice.finish_reason})")
                
                buffer += delta_content
                chunks_queue.append((chunk, delta_content))
                queue_size += 1
                logger.debug(f"混合审核围栏 [{self.guardrail_name}] 流式 (Chunk {chunk_counter}): Buffer长度: {len(buffer)}, 队列: {queue_size}")
                
                # --- 实时关键词检测 --- 
                # (在每个chunk累积后，达到delay_chunks或最后chunk时，对当前buffer进行检测)
                if queue_size > self.delay_chunks or is_last_chunk:
                    logger.debug(f"混合审核围栏 [{self.guardrail_name}] 流式 (Chunk {chunk_counter}): 触发关键词检测 - 队列={queue_size}, 延迟={self.delay_chunks}, 最后={is_last_chunk}")
                    
                    # 关键词检测逻辑 (来自原SensitiveWordGuardrail)
                    has_sensitive_keyword, found_keywords = self._contains_sensitive_word(buffer)
                    if has_sensitive_keyword:
                        keyword_error_message = self._format_error_message(found_keywords)
                        logger.warning(f"混合审核围栏 [{self.guardrail_name}] 流式关键词检测: 在输出 (buffer截止至Chunk {chunk_counter}) 中检测到敏感词。拦截原因: {keyword_error_message}")
                        raise ValueError(f"输出内容包含敏感词 (关键词检测): {keyword_error_message}") # 明确来源
                    
                    # 如果没有关键词问题，且不是最后一个chunk，则释放一个chunk
                    if chunks_queue and not is_last_chunk:
                        first_chunk_to_yield, _ = chunks_queue.pop(0)
                        queue_size -= 1
                        yield first_chunk_to_yield
                        logger.debug(f"混合审核围栏 [{self.guardrail_name}] 流式 (Chunk {chunk_counter}): 释放一个chunk (关键词检测通过)，剩余队列: {queue_size}")
            
            # --- 流处理正常结束后的LLM全面检测 --- 
            logger.debug(f"混合审核围栏 [{self.guardrail_name}] 流式: 流处理完成 (共 {chunk_counter} chunks)。对完整buffer (长度 {len(buffer)}) 进行LLM最终审核。剩余队列: {queue_size}")
            if buffer: # 只有当buffer中有内容时才进行LLM检查
                llm_violates_policy, llm_result_dict = await self._check_content_with_llm(buffer)
                if llm_violates_policy:
                    categories = ", ".join(llm_result_dict.get("categories", []))
                    error_message = f"LLM审核拦截完整流式输出: 内容策略违规。类别: [{categories}]"
                    logger.warning(f"混合审核围栏 [{self.guardrail_name}]: {error_message}")
                    raise ValueError(error_message) # 明确来源
                else:
                    logger.debug(f"混合审核围栏 [{self.guardrail_name}] 流式: 完整buffer通过LLM最终审核。")
            else:
                logger.debug(f"混合审核围栏 [{self.guardrail_name}] 流式: 完整buffer为空，跳过LLM最终审核。")

            # 释放队列中剩余的所有chunks (通常是最后一个chunk，或者当delay_chunks较大时积累的，且通过了所有检查)
            logger.debug(f"混合审核围栏 [{self.guardrail_name}] 流式: 释放队列中剩余 {queue_size} 个chunks。")
            for chunk_to_yield, _ in chunks_queue:
                yield chunk_to_yield
            chunks_queue.clear()
            queue_size = 0
            logger.debug(f"混合审核围栏 [{self.guardrail_name}] 流式: 所有剩余chunks已释放，队列清空。检查流程结束。")
                
        except ValueError as e:
            # 区分是关键词检测错误还是LLM检测错误，或者其他ValueError
            err_msg = str(e)
            if "关键词检测" in err_msg or "LLM审核拦截" in err_msg:
                logger.warning(f"混合审核围栏 [{self.guardrail_name}] 流式检查: 捕获到审核错误并向上传播: {err_msg}")
            else:
                logger.error(f"混合审核围栏 [{self.guardrail_name}] 流式检查: 捕获到未明确分类的ValueError: {err_msg}", exc_info=True)
            raise # 重新抛出，由上层处理
            
        except Exception as e:
            logger.error(f"混合审核围栏 [{self.guardrail_name}] 流式检查: 发生意外错误: {type(e).__name__}: {str(e)}", exc_info=True)
            raise 