# -*- encoding: utf-8 -*-
# @ModuleName: sensitive_words_splitter_llm.py
# @Author: Gemini
# @Time: 2024/07/04
# @Description: This script uses a single-stage, all-in-one LLM call to expand and validate sensitive words.

import pandas as pd
import json
import asyncio
import aiohttp
from tqdm import tqdm
from pathlib import Path

# --- All-in-One Prompt ---
ALL_IN_ONE_PROMPT = """
你是一位资深的内容安全工程师，精通模式分析和风险评估。你的任务是接收一个可能包含正则表达式的敏感词模式，并完成以下三项工作：

1.  **模式扩展**：
    *   将输入模式中所有使用 `|` 分隔的选项进行笛卡尔积展开。
    *   在展开过程中，**必须保持**所有原始的正则表达式元字符（如 `\\s*`, `\\b`, `\\d+` 等）不变。
    *   如果模式可以优化为更精准的正则表达式，请进行优化。

2.  **恶意性审查**：
    *   对每一个扩展出的结果（无论是具体短语还是新正则），判断其在中国大陆的法律、文化和网络环境下，是否明确具有恶意、违规或敏感的意图。
    *   标准必须严格，宁缺毋滥。一个本身中性的词（如“津巴布韦”）不应被标记为恶意。

3.  **类型一致性审查**：
    *   判断每一个扩展出的结果，其内容是否与给定的敏感类型 “{sensitive_type}” 完全匹配。

**输出格式要求**：
你的回答必须是一个JSON对象，其中包含一个名为 `results` 的数组。**数组中只应包含那些同时通过了“恶意性审查”和“类型一致性审查”的、最终有效的敏感词/规则**。如果一个扩展结果未通过任一审查，则**不应**出现在最终的 `results` 数组中。
你只需要返回一个JSON对象，不要包含任何额外的解释或注释。也不要包括```json```等标签

---
**示例 1**
输入模式: `(京|津)巴布韦`
指定的敏感类型: `地域歧视内容`
输出:
{
  "words": []
}

---
**示例 2**
输入模式: `武汉(人|民)\\s*(都是|全是|滚出去|滚蛋)`
指定的敏感类型: `地域歧视内容`
输出:
{
  "words": ["武汉人\\s*都是", "武汉人\\s*全是", "武汉人\\s*滚出去", "武汉人\\s*滚蛋", "武汉民\\s*都是", "武汉民\\s*全是", "武汉民\\s*滚出去", "武汉民\\s*滚蛋"]
}
---

现在，请处理以下输入：
输入模式: `{sensitive_word}`
指定的敏感类型: `{sensitive_type}`
输出:
"""

async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data, timeout=120) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception:
        answer = ""
    return answer

async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.24.45.213:50003/v1/chat/completions"
        messages = [{"role": "user", "content": point[question_name]}]
        body = {
            "model": "secllm-v3",
            "stream": False,
            "top_p": 0.01,
            "temperature": 0.01,
            "repetition_penalty": 1.05,
            "max_tokens": 4096,
            "messages": messages
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer.strip().startswith("{") and answer.strip().endswith("}"):
                break
            await asyncio.sleep(1)

        point[answer_name] = answer
        progress_bar.update(1)
        return point

async def run_llm_batch(data_list, question_name="prompt", answer_name="answer", desc="Running LLM Batch"):
    concurrency_limit = 100
    progress_bar = tqdm(total=len(data_list), desc=desc)
    connector = aiohttp.TCPConnector(limit_per_host=concurrency_limit)
    async with aiohttp.ClientSession(connector=connector) as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar, question_name=question_name, answer_name=answer_name) for data in data_list]
        results = await asyncio.gather(*tasks)
    progress_bar.close()
    return results

def parse_llm_json(response_str, context_word):
    """Safely parses JSON from LLM response."""
    if not isinstance(response_str, str):
        return None
    
    response_str = response_str.strip()
    if not response_str:
        return None

    try:
        if response_str.startswith("```json"):
            response_str = response_str.split("```json")[1].split("```")[0].strip()
        return json.loads(response_str)
    except (json.JSONDecodeError, AttributeError):
        # print(f"\n警告: 解析LLM JSON响应失败。上下文: '{context_word}'")
        return None

def run_all_in_one_process():
    """
    Main pipeline to expand and validate sensitive words in a single LLM call.
    """
    script_dir = Path(__file__).parent
    input_csv_path = script_dir / 'sensitive_words.csv'
    output_csv_path = script_dir / 'sensitive_words_split_aio.csv'

    if not input_csv_path.exists():
        print(f"错误：输入文件不存在于 '{input_csv_path}'")
        return

    print(f"正在从 '{input_csv_path}' 读取敏感词...")
    df = pd.read_csv(input_csv_path)
    original_count = len(df)
    print(f"原始敏感词数量: {original_count}")

    # --- Prepare and Execute All-in-One LLM Tasks ---
    tasks = df.to_dict('records')
    for task in tasks:
        task['prompt'] = ALL_IN_ONE_PROMPT.replace("{sensitive_word}", task['敏感词']).replace("{sensitive_type}", task['敏感类型'])

    loop = asyncio.get_event_loop()
    results = loop.run_until_complete(
        run_llm_batch(tasks, answer_name="llm_response", desc="正在一次性处理...")
    )

    # --- Process Results and Save ---
    final_data = []
    print("\n正在处理LLM返回结果并生成最终文件...")

    for result in tqdm(results, desc="过滤和保存"):
        llm_response_str = result.get("llm_response")
        parsed_data = parse_llm_json(llm_response_str, result['敏感词'])

        if (isinstance(parsed_data, dict) and
            isinstance(parsed_data.get("words"), list)):
            
            for item in parsed_data["words"]:
                if (isinstance(item, str) and item.strip()):
                    final_data.append({
                        '敏感词': item.strip(),
                        '敏感类型': result['敏感类型']
                    })

    # Save validated data
    validated_df = pd.DataFrame(final_data).drop_duplicates()
    validated_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')

    new_count = len(validated_df)
    print("\n处理完成！")
    print(f"最终通过验证的敏感词已保存到: '{output_csv_path}'")
    print(f"\n处理统计：")
    print(f"  - 原始词条数: {original_count}")
    print(f"  - 最终有效词条数: {new_count}")

if __name__ == '__main__':
    run_all_in_one_process() 