import re
import asyncio
from typing import Any, Dict, List, Literal, Optional, Union, AsyncGenerator, Pattern, Tuple

import litellm
# from litellm._logging import verbose_logger
from litellm.caching.caching import DualCache
from litellm.integrations.custom_guardrail import CustomGuardrail
from litellm.proxy._types import UserAPIKeyAuth
from litellm.types.utils import ModelResponseStream
from litellm.types.guardrails import GuardrailEventHooks  

import logging

verbose_logger = logging.getLogger()
verbose_logger.setLevel(level=logging.DEBUG)
formatter = logging.Formatter(fmt='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
                              datefmt='%m/%d/%Y %H:%M:%S')
console = logging.StreamHandler()
console.setLevel(logging.INFO)
console.setFormatter(formatter)
verbose_logger.addHandler(console)

class PIIMaskingGuardrail(CustomGuardrail):
    # 预定义的敏感信息类型和对应的正则表达式模式
    DEFAULT_PII_PATTERNS = {
        # 通用类型
        "email": r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}",
        "url": r"https?://(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)",
        "ip_address": r"\b(?:\d{1,3}\.){3}\d{1,3}\b",
        # 中国特定类型
        "cn_mobile": r"(?:\+?86)?1[3-9]\d{9}",
        "cn_id_card": r"\b[1-9]\d{5}(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}(?:\d|X|x)\b",
        # "cn_name": r"[\u4e00-\u9fa5]{2,4}", # 简单中文名模式易误报，默认禁用
        "cn_bank_card": r"\b\d{16,19}\b",
        "cn_address": r"(?P<prefix>[\u4e00-\u9fa5]{2,}(?:省|市|自治区|特别行政区)[\u4e00-\u9fa5]{2,}(?:市|区|县|自治县|旗|自治旗|地区|盟)[\u4e00-\u9fa5]{0,}(?:区|县|街道|镇|乡)?)(?P<detail>[\u4e00-\u9fa5\d\-\s\(\)（）号楼栋单元室]+)(?P<suffix>(?:路|街|巷|弄|号|院|小区|大厦|村|组|栋|单元|楼|层|室)\d*|\d+号?)(?![，。？！；])",
        "date_of_birth": r"\b(?:(?:19|20)\d{2})[-/.年](?:0?[1-9]|1[0-2])[-/.月](?:0?[1-9]|[12]\d|3[01])日?\b",
    }

    # 用于中文地址特殊处理的关键字列表
    _ADDRESS_KEYWORDS_TO_PRESERVE = [
        "省", "市", "区", "县", "镇", "乡", "村",
        "路", "街", "巷", "弄", "号", "院", "小区",
        "大厦", "广场", "中心", "花园", "山庄", "公寓",
        "座", "栋", "幢", "单元", "楼", "层", "室", "房", "排"
    ]

    def __init__(
        self,
        pii_types: List[str] = None,
        custom_patterns: Dict[str, str] = None,
        mask_char: str = "*",
        show_preview: bool = True,
        preview_length: int = 2,
        delay_chunks: int = 15,
        default_on: bool = False,
        **kwargs,
    ):
        """
        敏感信息过滤围栏，替换输出中的个人敏感信息 (PII)。

        Args:
            pii_types (List[str], optional): 要检测的 PII 类型列表。
                                             默认为中国大陆常用类型 (如 email, cn_mobile, cn_id_card, cn_address 等)。
                                             可用的默认类型键名包括 "email", "url", "ip_address", "cn_mobile", 
                                             "cn_id_card", "cn_bank_card", "cn_address", "date_of_birth"。
                                             "cn_name" (简单中文名) 类型因易产生误报而默认不包含在此列表中，
                                             用户如需检测中文名，建议通过 `custom_patterns` 提供更精确的正则，
                                             或在 `pii_types` 中明确加入 "cn_name" 以使用内置的简单模式 (需谨慎评估误报风险)。
                                             设置为 `[]` 将不使用任何默认类型 (仍可通过 `custom_patterns` 添加)。
                                             设置为 `None` (默认) 将使用预设的中国大陆常用类型列表。
            custom_patterns (Dict[str, str], optional): 自定义 PII 正则表达式模式字典。
                                                        键是模式名称 (如 "my_custom_id")，值是正则表达式字符串。
                                                        自定义模式可以覆盖同名的默认模式，或添加新的PII类型。
            mask_char (str, optional): 用于替换 PII 的字符。默认为 "*"。
            show_preview (bool, optional): 是否显示 PII 的前几个字符作为预览。默认为 True。
            preview_length (int, optional): 如果 `show_preview` 为 True，显示的预览字符数量。默认为 2。
            delay_chunks (int, optional): 在流式输出中，延迟处理的 chunk 数量。
                                           用于缓冲内容以检测跨 chunk 的 PII。默认为 15 (原为5)。
            default_on (bool, optional): 此 Guardrail 是否默认启用。默认为 False。
            **kwargs: 其他传递给基类 `CustomGuardrail` 的参数。
        """
        # 初始化配置 - 默认使用中国大陆常用 PII 类型
        verbose_logger.info("开始初始化自定义围栏：PIIMaskingGuardrail")
        self.pii_types = pii_types if pii_types is not None else [
            "email",
            "url",
            "ip_address",
            "cn_mobile",
            "cn_id_card",
            # "cn_name", # 默认禁用，原因见文档字符串。简单中文名模式: r"[\u4e00-\u9fa5]{2,4}"
            "cn_bank_card",
            "cn_address",
            "date_of_birth",
        ]
        self.mask_char = mask_char
        self.show_preview = show_preview
        self.preview_length = preview_length
        self.delay_chunks = delay_chunks

        # 编译正则表达式
        self.patterns = {}
        for pii_type in self.pii_types:
            if pii_type in self.DEFAULT_PII_PATTERNS:
                try:
                    self.patterns[pii_type] = re.compile(self.DEFAULT_PII_PATTERNS[pii_type])
                except re.error as e:
                     verbose_logger.error(f"无法编译默认 PII 模式 '{pii_type}': {e}")


        if custom_patterns:
            for pattern_name, pattern in custom_patterns.items():
                if isinstance(pattern, str):
                    try:
                        self.patterns[pattern_name] = re.compile(pattern)
                        # 如果提供了自定义模式，但未在 pii_types 中明确列出，
                        # 确保它包含在内部使用的模式中（会覆盖同名默认模式）
                        if pattern_name not in self.pii_types:
                            # 可以选择在这里将其添加到 self.pii_types 列表中，
                            # 或者仅在内部 self.patterns 中使用它。当前选择后者。
                            pass
                    except re.error as e:
                        verbose_logger.error(f"无法编译自定义 PII 模式 '{pattern_name}': {e}")
                else:
                     verbose_logger.error(f"无效的自定义 PII 模式 '{pattern_name}': 需要字符串，得到 {type(pattern).__name__}")

        active_pii_types = list(self.patterns.keys())
        self.optional_params = kwargs #  将 optional_params 的赋值提前
        self.default_on = default_on
        self.guardrail_name = kwargs.get("guardrail_name", "pii_guardrail")
        
        if active_pii_types:
            verbose_logger.info(f"PIIMaskingGuardrail (名称: '{self.guardrail_name}') 初始化完成。活动的 PII 类型: {active_pii_types}。默认启用状态: {self.default_on}。")
        else:
            verbose_logger.warning(f"PIIMaskingGuardrail (名称: '{self.guardrail_name}') 初始化完成，但没有任何活动的 PII 模式。默认启用状态: {self.default_on}。")

        super().__init__(**kwargs)
        # verbose_logger.info(f"PIIMaskingGuardrail 初始化完成: guardrail_name={self.guardrail_name}, default_on={self.default_on}") # 这条信息已合并

    # def should_run_guardrail(self, data, event_type):  
    #     # 确保在流式输出处理时运行  
    #     if event_type == GuardrailEventHooks.post_call:  
    #         return True  
    #     return False  
    
    def _mask_pii(self, text: str, return_found: bool = False) -> Union[str, Tuple[str, Dict[str, List[str]]]]:
        """
        将文本中的敏感信息 (PII) 替换为遮蔽字符。

        Args:
            text (str): 要处理的文本。
            return_found (bool, optional): 是否同时返回找到的 PII 信息字典。默认为 False。

        Returns:
            Union[str, Tuple[str, Dict[str, List[str]]]]:
                如果 `return_found` 为 False，返回掩码处理后的文本字符串。
                如果 `return_found` 为 True，返回一个元组 `(masked_text, found_pii_dict)`，
                其中 `masked_text` 是掩码后的文本，`found_pii_dict` 是一个字典，
                键是 PII 类型 (如 "email", "cn_mobile")，值是该类型下找到的原始 PII 字符串列表。
        """
        if not text or not isinstance(text, str):
            return text if not return_found else (text, {})

        # 存储找到的 PII 片段信息：(起始索引, 结束索引, PII类型, 原始匹配文本, 预计算的替换字符串)
        pii_spans_with_replacement = []

        # 1. 查找所有 PII 匹配项并预计算替换字符串
        for pii_type, pattern in self.patterns.items():
            try:
                for match in pattern.finditer(text):
                    full_match_text = match.group(0)
                    if not full_match_text:
                        continue

                    replacement_str = ""

                    # --- 特殊处理：中文地址 (cn_address) ---
                    # 目标：保留地址中的 省/市/区/县/乡/镇/村/路/街/巷/弄/号/院/小区/大厦/单元/楼/层/室 等关键字，
                    # 以及第一个关键字之前的所有字符，其余部分用掩码字符替换。
                    if pii_type == "cn_address":
                        try:
                            if not full_match_text: # 空匹配直接返回空替换
                                replacement_str = ""
                            else:
                                text_len = len(full_match_text)
                                # 标记每个字符是否属于要保留的关键字或在第一个关键字之前
                                preserve_char = [False] * text_len
                                first_kw_char_index = -1

                                # 查找所有关键字的匹配范围
                                kw_spans = []
                                for kw in self._ADDRESS_KEYWORDS_TO_PRESERVE:
                                    try:
                                        for kw_match in re.finditer(re.escape(kw), full_match_text):
                                            kw_spans.append(kw_match.span())
                                    except re.error as find_err:
                                        verbose_logger.debug(f"查找地址关键字 '{kw}' 时正则错误: {find_err}") # 改为 debug
                                        continue
                                
                                if kw_spans:
                                    # 按起始位置排序，并合并重叠/相邻的关键字区间
                                    kw_spans.sort()
                                    merged_spans = [kw_spans[0]]
                                    for current_start, current_end in kw_spans[1:]:
                                        prev_start, prev_end = merged_spans[-1]
                                        if current_start <= prev_end: # 重叠或相邻
                                            merged_spans[-1] = (prev_start, max(prev_end, current_end))
                                        else:
                                            merged_spans.append((current_start, current_end))
                                    
                                    # 标记所有合并后关键字区间的字符
                                    for start_kw, end_kw in merged_spans:
                                        for i in range(start_kw, end_kw):
                                            if i < text_len:
                                                preserve_char[i] = True
                                    
                                    # 找到第一个关键字字符的索引
                                    for idx, preserved in enumerate(preserve_char):
                                        if preserved: # 第一个属于关键字区间的字符
                                            first_kw_char_index = idx
                                            break
                                
                                # 标记第一个关键字字符之前的所有字符
                                if first_kw_char_index != -1:
                                    for i in range(first_kw_char_index):
                                        preserve_char[i] = True
                                elif not kw_spans: # 如果没有找到任何关键字，则全部不保留 (除非预览逻辑覆盖)
                                    pass # preserve_char 保持全 False

                                # 构建掩码后的地址字符串
                                masked_chars_addr_list = []
                                for i, char_original in enumerate(full_match_text):
                                    if preserve_char[i]:
                                        masked_chars_addr_list.append(char_original)
                                    else:
                                        masked_chars_addr_list.append(self.mask_char)
                                replacement_str = "".join(masked_chars_addr_list)

                        except Exception as e: # 通用异常捕获
                             verbose_logger.error(f"处理中文地址 '{full_match_text}' 时发生未知错误: {e}。将回退到标准掩码处理。")
                             # 出错时回退到标准预览或完全掩码逻辑
                             if self.show_preview and len(full_match_text) > self.preview_length:
                                 replacement_str = full_match_text[:self.preview_length] + self.mask_char * (len(full_match_text) - self.preview_length)

                    # --- 其他 PII 类型的默认处理 ---
                    else:
                        if self.show_preview and len(full_match_text) > self.preview_length:
                            replacement_str = full_match_text[:self.preview_length] + self.mask_char * (len(full_match_text) - self.preview_length)
                        else:
                            replacement_str = self.mask_char * len(full_match_text)

                    pii_spans_with_replacement.append((match.start(), match.end(), pii_type, full_match_text, replacement_str))

            except Exception as e:
                verbose_logger.error(f"使用模式 {pii_type} 进行 finditer 时出错: {e}")
                continue

        if not pii_spans_with_replacement:
            return text if not return_found else (text, {})

        # 2. 按起始位置对找到的 PII 片段排序
        pii_spans_with_replacement.sort(key=lambda x: x[0])

        # 3. 解决重叠：保留不重叠的片段（优先保留先出现的）
        #    遍历排序后的所有 PII 候选片段。
        #    如果一个片段的起始位置晚于或等于上一个已选定用于掩码的片段的结束位置，
        #    则该片段不与已选定的片段重叠，可以被选定用于掩码。
        #    否则，该片段与已选定的片段重叠，根据当前策略（优先保留先出现的），此片段将被忽略。
        non_overlapping_spans = []
        last_masked_end = 0
        for span_data in pii_spans_with_replacement:
            start, end, _, _, _ = span_data
            if start >= last_masked_end:
                non_overlapping_spans.append(span_data)
                last_masked_end = end
            # 当前策略：简单丢弃后出现的重叠片段

        if not non_overlapping_spans:
             return text if not return_found else (text, {})

        found_pii = {} if return_found else None
        has_changes = False
        # 仅在确定需要修改时才创建字符列表
        masked_chars = None # 推迟 list(text) 的创建

        # 4. 根据非重叠片段应用预计算的替换字符串
        for start, end, pii_type, matched_text, replacement_str in non_overlapping_spans:
            # 优化：仅在第一次需要修改时才实际转换 text 为 list
            if masked_chars is None:
                current_slice_original_text = text[start:end]
            else:
                current_slice_original_text = "".join(masked_chars[start:end]) # 如果已经转换，从列表获取
            
            # 只有当替换内容与原始内容不同时，才进行替换并标记更改
            if replacement_str != current_slice_original_text:
                if masked_chars is None: # 第一次实际修改，创建列表
                    masked_chars = list(text)
                
                if 0 <= start <= end <= len(masked_chars):
                    masked_chars[start:end] = list(replacement_str)
                    has_changes = True
                    if len(current_slice_original_text) != len(replacement_str):
                         # 记录长度不匹配，可能指示掩码逻辑或正则模式问题
                         verbose_logger.debug(f"掩码片段长度不匹配: 原始 {len(current_slice_original_text)} vs 替换 {len(replacement_str)} (类型: {pii_type}, 位置: {start}-{end})") # 改为 debug 日志
                else:
                     verbose_logger.error(f"无效的 PII 片段索引: 类型={pii_type}, start={start}, end={end}, text_len={len(text if masked_chars is None else masked_chars)}")
                     continue # 跳过无效片段

            # 如果需要，记录找到的原始 PII 文本
            if return_found:
                 if pii_type not in found_pii:
                     found_pii[pii_type] = []
                 # 避免重复记录完全相同的 PII 字符串
                 if matched_text not in found_pii[pii_type]:
                    found_pii[pii_type].append(matched_text)

        # 5. 如果没有实际发生更改，返回原始文本以优化性能
        if not has_changes and not return_found:
             return text

        # 如果有更改但 masked_chars 未被初始化 (例如，所有 replacement_str 都恰好等于原始片段，但我们需要 found_pii)
        # 或者没有更改但需要 found_pii (这种情况应该在前面 non_overlapping_spans 为空时处理，但作为安全措施)
        if masked_chars is None:
            # 如果有找到的 PII (return_found is True)，即使文本内容没变，也要确保返回原始文本和 found_pii
            return (text, found_pii) if return_found else text

        final_masked_text = "".join(masked_chars)
        return (final_masked_text, found_pii) if return_found else final_masked_text

    async def async_pre_call_hook(
        self,
        user_api_key_dict: UserAPIKeyAuth,
        cache: DualCache,
        data: dict,
        call_type: Literal[
            "completion",
            "text_completion",
            "embeddings",
            "image_generation",
            "moderation",
            "audio_transcription",
            "pass_through_endpoint",
            "rerank"
        ],
    ) -> Optional[Union[Exception, str, dict]]:
        """(Guardrail Hook) 在调用 LLM 之前执行。此钩子仅传递输入，不进行修改。"""
        return data

    async def async_post_call_success_hook(
        self,
        data: dict,
        user_api_key_dict: UserAPIKeyAuth,
        response,
    ):
        """(Guardrail Hook) 在成功调用 LLM 后执行。替换 LLM 输出（非流式）中的 PII。"""
        if isinstance(response, litellm.ModelResponse):
            for choice in response.choices:
                if hasattr(choice, "message") and hasattr(choice.message, "content") and choice.message.content:
                    content = choice.message.content
                    masked_content, found_pii = self._mask_pii(content, return_found=True)

                    if found_pii: # 仅当找到 PII 时记录和修改
                        pii_items_count = sum(len(items) for items in found_pii.values())
                        verbose_logger.info(
                            f"PII掩码：在非流式响应中检测到并掩码了 {pii_items_count} 个PII实例，类型: {list(found_pii.keys())}"
                        )
                        choice.message.content = masked_content
        # else: # 如果不是预期的响应类型，则不处理

    async def async_post_call_streaming_iterator_hook(
        self,
        user_api_key_dict: UserAPIKeyAuth,
        response: Any,
        request_data: dict,
    ) -> AsyncGenerator[ModelResponseStream, None]:
        """
        (Guardrail Hook) 处理流式输出，检测并替换 PII。

        使用延迟处理机制：缓冲 `delay_chunks` 指定数量的块，
        以确保在输出每个块之前，有足够的上下文来检测可能跨越块边界的 PII。
        """
        verbose_logger.info("PII掩码：开始处理流式响应...")
        buffer = ""  # 累积接收到的内容，用于 PII 检测
        # 队列存储元组: (原始 chunk 对象, 该 chunk 的原始 delta 内容, 该 chunk 到达时 buffer 的长度)
        chunks_queue: List[Tuple[Any, str, int]] = []

        async for chunk in response:
            original_delta = ""
            is_last_chunk = False
            if hasattr(chunk, "choices") and len(chunk.choices) > 0:
                choice = chunk.choices[0]
                if hasattr(choice, "delta") and hasattr(choice.delta, "content"):
                    delta_content_value = choice.delta.content
                    if delta_content_value is not None:
                        original_delta = delta_content_value
                        verbose_logger.debug(f"流式调试: 收到新 chunk，delta 内容: '{original_delta[:50]}{'...' if len(original_delta) > 50 else ''}'") # 截断过长内容
                # 检查是否是最后一个 chunk
                if hasattr(choice, "finish_reason") and choice.finish_reason is not None:
                    is_last_chunk = True
                    verbose_logger.debug("流式调试: 检测到最后一个 chunk")

            buffer += original_delta
            chunks_queue.append((chunk, original_delta, len(buffer)))
            verbose_logger.debug(f"流式调试: 当前 buffer 长度: {len(buffer)}, chunks_queue 长度: {len(chunks_queue)}")

            # 当队列中的 chunk 数量超过延迟设置，或者收到最后一个 chunk 时，开始处理并输出队首的 chunk
            should_yield = len(chunks_queue) > self.delay_chunks

            if should_yield or is_last_chunk:
                verbose_logger.debug(f"流式调试: 触发处理条件: should_yield={should_yield}, is_last_chunk={is_last_chunk}")
                if not chunks_queue:
                    verbose_logger.debug("流式调试: chunks_queue 为空，跳过处理")
                    continue

                # 获取队列中最旧的 chunk 信息
                chunk_to_yield, oldest_original_delta, buffer_len_at_arrival = chunks_queue[0]
                verbose_logger.debug(f"流式调试: 处理最旧的 chunk，delta 内容: '{oldest_original_delta[:50]}{'...' if len(oldest_original_delta) > 50 else ''}'")

                # 使用完整的 buffer 进行 PII 检测
                masked_buffer, found_pii = self._mask_pii(buffer, return_found=True)
                if found_pii:
                    verbose_logger.debug(f"流式调试: 在完整 buffer 中检测到 PII: {found_pii}")
                
                # 计算 oldest_original_delta 在 buffer 中的起止索引
                start_index = buffer_len_at_arrival - len(oldest_original_delta)
                end_index = buffer_len_at_arrival

                masked_delta = oldest_original_delta # 默认使用原始 delta
                # 从掩码后的完整 buffer 中提取对应的 delta 部分
                if 0 <= start_index <= end_index <= len(masked_buffer):
                    masked_delta = masked_buffer[start_index:end_index]
                    if masked_delta != oldest_original_delta:
                        verbose_logger.debug(f"流式调试: 块内容已被掩码处理，原始: '{oldest_original_delta[:50]}{'...' if len(oldest_original_delta) > 50 else ''}', 掩码后: '{masked_delta[:50]}{'...' if len(masked_delta) > 50 else ''}'")
                else:
                    verbose_logger.error(
                        f"流式处理中索引计算无效: "
                        f"start={start_index}, end={end_index}, masked_len={len(masked_buffer)}, "
                        f"orig_delta_len={len(oldest_original_delta)}, buffer_len={buffer_len_at_arrival}"
                    )

                # 如果原始 delta 非空且掩码处理后的 delta 与原始不同，则更新 chunk 内容
                if oldest_original_delta and masked_delta != oldest_original_delta:
                    if (
                        hasattr(chunk_to_yield, "choices") and len(chunk_to_yield.choices) > 0 and
                        hasattr(chunk_to_yield.choices[0], "delta") and
                        hasattr(chunk_to_yield.choices[0].delta, "content")
                    ):
                        chunk_to_yield.choices[0].delta.content = masked_delta
                        verbose_logger.info(f"PII掩码：已在流式 chunk 中替换 PII。原始片段: '{oldest_original_delta[:50]}{'...' if len(oldest_original_delta) > 50 else ''}', 掩码后: '{masked_delta[:50]}{'...' if len(masked_delta) > 50 else ''}'")
                    else:
                        verbose_logger.warning("PII掩码：无法更新流式 chunk 内容，因其结构不符合预期。")

                # 弹出队首 chunk 并将其输出
                yield chunks_queue.pop(0)[0]

        # --- 流结束后的简化处理 ---
        verbose_logger.debug(f"流式调试: 流处理主循环结束。剩余 chunks_queue 长度: {len(chunks_queue)}")
        # 注意：此时 chunks_queue 应该为空，因为所有块都已在主循环中处理
        if chunks_queue:
            verbose_logger.warning(f"PII掩码：流处理结束后 chunks_queue 尚有 {len(chunks_queue)} 个未处理的块。将尝试处理...")
            
            # 首先对完整的buffer进行一次PII掩码处理 (可能冗余，但作为安全措施)
            masked_final_buffer, found_pii_in_final = self._mask_pii(buffer, return_found=True)
            if found_pii_in_final:
                verbose_logger.debug(f"流式调试: 在最终完整buffer中检测到 PII: {found_pii_in_final}")
            
            # 处理任何剩余的块，对它们应用PII掩码处理
            for chunk, original_delta, buffer_len_at_arrival in chunks_queue:
                if not original_delta:  # 跳过空delta
                    verbose_logger.debug("流式调试: 跳过空delta的chunk")
                    yield chunk
                    continue
                
                # 计算原始delta在buffer中的起止索引
                start_index = buffer_len_at_arrival - len(original_delta)
                end_index = buffer_len_at_arrival
                
                masked_delta = original_delta  # 默认使用原始delta
                # 从掩码后的buffer中提取对应的delta部分
                if 0 <= start_index <= end_index <= len(masked_final_buffer):
                    masked_delta = masked_final_buffer[start_index:end_index]
                    if masked_delta != original_delta:
                        verbose_logger.debug(f"流式调试: 最终块内容已被掩码处理，原始: '{original_delta[:50]}{'...' if len(original_delta) > 50 else ''}', 掩码后: '{masked_delta[:50]}{'...' if len(masked_delta) > 50 else ''}'")
                else:
                    verbose_logger.error(
                        f"流式处理最终循环中索引计算无效: "
                        f"start={start_index}, end={end_index}, masked_len={len(masked_final_buffer)}, "
                        f"orig_delta_len={len(original_delta)}, buffer_len={buffer_len_at_arrival}"
                    )
                
                # 如果原始delta非空且掩码处理后的delta与原始不同，则更新chunk内容
                if masked_delta != original_delta:
                    if (
                        hasattr(chunk, "choices") and len(chunk.choices) > 0 and
                        hasattr(chunk.choices[0], "delta") and
                        hasattr(chunk.choices[0].delta, "content")
                    ):
                        chunk.choices[0].delta.content = masked_delta
                        verbose_logger.info(f"PII掩码：已在流末尾的剩余 chunk 中替换 PII (掩码后delta: '{masked_delta[:50]}{'...' if len(masked_delta) > 50 else ''}')")
                    else:
                        verbose_logger.warning("PII掩码：无法更新流末尾的剩余 chunk 内容，结构不匹配。")
                
                # 输出处理后的chunk
                yield chunk
        verbose_logger.info("PII掩码：流式响应处理完成。")
