import curses
import pandas as pd
import sys
import argparse
import textwrap
import os

# --- 依赖与新功能 ---
try:
    from wcwidth import wcswidth
    # 标记wcwidth是否成功导入
    WCWIDTH_AVAILABLE = True
except ImportError:
    # 如果wcwidth未安装，则进行优雅降级，使用len作为后备
    wcswidth = len
    WCWIDTH_AVAILABLE = False

def cjk_aware_wrap(text: str, width: int) -> list[str]:
    """
    一个能感知东亚字符宽度的文本换行函数，可以正确处理中英文混合文本。
    """
    lines = []
    
    # 首先按换行符分割，以保留段落
    for para in str(text).split('\n'):
        if not para:
            lines.append('')
            continue
        
        # 对单个段落进行处理
        current_line = []
        current_width = 0
        
        # 按空格分割单词，这对于英文换行至关重要
        # 对于没有空格的中文，整段话会成为一个"单词"
        words = para.split(' ')
        
        for i, word in enumerate(words):
            word_width = wcswidth(word)
            
            # 如果一个单词本身就超长，必须在单词内部强制换行
            if word_width > width:
                # 先将当前行中已有的内容存起来
                if current_line:
                    lines.append(' '.join(current_line))
                    current_line = []
                    current_width = 0
                    
                # 然后逐字符处理这个超长的单词
                long_word_buffer = ""
                for char in word:
                    char_width = wcswidth(char)
                    if wcswidth(long_word_buffer) + char_width > width:
                        lines.append(long_word_buffer)
                        long_word_buffer = ""
                    long_word_buffer += char
                
                # 将超长单词处理完后剩下的部分作为新行的开始
                if long_word_buffer:
                    current_line.append(long_word_buffer)
                    current_width = wcswidth(long_word_buffer)

            # 如果加上新单词超出了宽度，则换行
            # `(1 if current_line else 0)` 是用来计算单词间空格的宽度
            elif current_width + word_width + (1 if current_line else 0) > width:
                lines.append(' '.join(current_line))
                current_line = [word]
                current_width = word_width
            
            # 否则，将单词加入当前行
            else:
                current_line.append(word)
                current_width += word_width + (1 if len(current_line) > 1 else 0)

        # 不要忘记添加最后一行
        if current_line:
            lines.append(' '.join(current_line))
            
    return lines or ['']

# --- 颜色定义 ---
COLOR_DEFAULT = 1
COLOR_TITLE = 2
COLOR_HIGHLIGHT = 3
COLOR_SUCCESS = 4
COLOR_WARNING = 5
COLOR_INFO = 6

def init_colors():
    """初始化curses颜色对"""
    curses.start_color()
    curses.use_default_colors()
    curses.init_pair(COLOR_DEFAULT, curses.COLOR_WHITE, -1)
    curses.init_pair(COLOR_TITLE, curses.COLOR_CYAN, -1)
    curses.init_pair(COLOR_HIGHLIGHT, curses.COLOR_YELLOW, -1)
    curses.init_pair(COLOR_SUCCESS, curses.COLOR_GREEN, -1)
    curses.init_pair(COLOR_WARNING, curses.COLOR_RED, -1)
    curses.init_pair(COLOR_INFO, curses.COLOR_BLUE, -1)

def draw_text(stdscr, y, x, text, color_pair, bold=False):
    """在指定位置绘制文本"""
    attr = curses.color_pair(color_pair)
    if bold:
        attr |= curses.A_BOLD
    stdscr.addstr(y, x, text, attr)

def draw_wrapped_text(stdscr, y, x, text, width, color_pair):
    """绘制自动换行的文本"""
    wrapper = textwrap.TextWrapper(width=width)
    lines = wrapper.wrap(text=text)
    if not lines: # 如果文本为空，也至少占一行
        lines = ['']
    for i, line in enumerate(lines):
        draw_text(stdscr, y + i, x, line, color_pair)
    return len(lines)

def get_wrapped_lines(text, width):
    """辅助函数，调用cjk_aware_wrap来计算换行。"""
    return cjk_aware_wrap(text, width)

def draw_interface(stdscr, df, index, scroll_offset, status_message=""):
    """绘制主界面"""
    stdscr.clear()
    height, width = stdscr.getmaxyx()
    content_width = width - 4
    footer_y = height - 3
    content_height_available = footer_y - 3

    # --- 内容高度预计算 (Dry Run) ---
    record = df.iloc[index]
    question_lines = get_wrapped_lines(str(record.get('大模型的输入内容', 'N/A')), content_width)
    answer_lines = get_wrapped_lines(str(record.get('生成的回答', 'N/A')), content_width)
    reasoning_lines = get_wrapped_lines(f"思路: {record.get('reasoning', 'N/A')}", content_width)
    analysis_lines = get_wrapped_lines(
        f"风险判断: {record.get('is_risky', 'N/A')} | "
        f"回答分类: {record.get('response_classification', 'N/A')} | "
        f"风险类型: {record.get('risk_types', 'N/A')}",
        content_width
    )
    
    total_content_height = (
        1 + len(question_lines) + 1 + # title + content + separator
        1 + len(answer_lines) + 1 +
        1 + len(reasoning_lines) + 1 + 
        len(analysis_lines)
    )

    is_scrollable = total_content_height > content_height_available
    
    # --- 顶部状态栏 ---
    label_status = record.get('manual_label', -1)
    status_text = "尚未标注"
    status_color = COLOR_HIGHLIGHT
    if label_status == 1:
        status_text = "已标注: 1 (有风险)"
        status_color = COLOR_WARNING
    elif label_status == 0:
        status_text = "已标注: 0 (无风险)"
        status_color = COLOR_SUCCESS

    header_text = f"样本 [{index + 1}/{len(df)}] | 状态: "
    draw_text(stdscr, 0, 1, header_text, COLOR_DEFAULT)
    draw_text(stdscr, 0, 1 + len(header_text), status_text, status_color, bold=True)
    
    if is_scrollable:
        scroll_indicator = " [可滚动]"
        draw_text(stdscr, 0, 1 + len(header_text) + len(status_text), scroll_indicator, COLOR_INFO, bold=True)

    if status_message:
        draw_text(stdscr, 0, width - len(status_message) - 2, status_message, COLOR_HIGHLIGHT, bold=True)
    else:
        quit_text = "q:退出"
        draw_text(stdscr, 0, width - len(quit_text) - 2, quit_text, COLOR_DEFAULT)

    stdscr.hline(1, 1, '-', width - 2)

    # --- 内容区 (带滚动绘制) ---
    virtual_y = 3
    
    def draw_content_block(title, lines):
        nonlocal virtual_y
        # 绘制标题
        draw_y = virtual_y - scroll_offset
        if 1 < draw_y < footer_y:
            draw_text(stdscr, draw_y, 2, title, COLOR_TITLE, bold=True)
        virtual_y += 1
        
        # 绘制内容
        for line in lines:
            draw_y = virtual_y - scroll_offset
            if 1 < draw_y < footer_y:
                draw_text(stdscr, draw_y, 2, line, COLOR_DEFAULT)
            virtual_y += 1
        
        # 绘制分隔符
        draw_y = virtual_y - scroll_offset
        if 1 < draw_y < footer_y:
            stdscr.hline(draw_y, 1, '-', width - 2)
        virtual_y += 1

    draw_content_block("[ 问题 (Q) ]", question_lines)
    draw_content_block("[ 模型回答 (A) ]", answer_lines)

    # --- LLM 分析结果 (特殊处理，无底部横线) ---
    # 绘制标题
    draw_y = virtual_y - scroll_offset
    if 1 < draw_y < footer_y:
        draw_text(stdscr, virtual_y - scroll_offset, 2, "[ LLM 分析结果 ]", COLOR_TITLE, bold=True)
    virtual_y += 1

    # 绘制思路
    for line in reasoning_lines:
        draw_y = virtual_y - scroll_offset
        if 1 < draw_y < footer_y:
            draw_text(stdscr, draw_y, 2, line, COLOR_DEFAULT)
        virtual_y += 1
    virtual_y += 1 # 增加额外间距

    # 绘制其他分析
    for line in analysis_lines:
        draw_y = virtual_y - scroll_offset
        if 1 < draw_y < footer_y:
            draw_text(stdscr, draw_y, 2, line, COLOR_DEFAULT)
        virtual_y += 1

    # --- 底部操作栏 ---
    stdscr.hline(footer_y, 1, '-', width - 2)
    instructions = "↑/↓: 导航 | PgUp/PgDn: 滚动 | 0/1: 标注 | s: 保存 | q: 退出"
    draw_text(stdscr, footer_y + 1, 2, instructions, COLOR_DEFAULT)

    # 您的人工标注
    manual_label_text = "您的人工标注: "
    draw_text(stdscr, footer_y + 2, 2, manual_label_text, COLOR_TITLE, bold=True)
    
    label_display = "尚未标注"
    label_color = COLOR_HIGHLIGHT
    if label_status == 1:
        label_display = "1 (有风险)"
        label_color = COLOR_WARNING
    elif label_status == 0:
        label_display = "0 (无风险)"
        label_color = COLOR_SUCCESS
    
    draw_text(stdscr, footer_y + 2, 2 + len(manual_label_text), label_display, label_color, bold=True)

    stdscr.refresh()
    return total_content_height, content_height_available


def main_loop(stdscr, df, filepath, start_idx):
    """主事件循环"""
    curses.curs_set(0) # 隐藏光标
    init_colors()
    
    current_idx = start_idx
    scroll_offset = 0
    status_message = ""
    
    while True:
        total_h, available_h = draw_interface(stdscr, df, current_idx, scroll_offset, status_message)
        status_message = "" # 重置消息，使其只显示一次
        
        key = stdscr.getch()

        if key == curses.KEY_UP:
            current_idx = max(0, current_idx - 1)
            scroll_offset = 0
        elif key == curses.KEY_DOWN:
            current_idx = min(len(df) - 1, current_idx + 1)
            scroll_offset = 0
        elif key == curses.KEY_PPAGE: # Page Up
            scroll_offset = max(0, scroll_offset - available_h // 2)
        elif key == curses.KEY_NPAGE: # Page Down
            max_scroll = max(0, total_h - available_h)
            scroll_offset = min(max_scroll, scroll_offset + available_h // 2)
        elif key == ord('0'):
            df.loc[df.index[current_idx], 'manual_label'] = 0
            df.to_csv(filepath, index=False, encoding='utf-8')
            status_message = "已标注为 0 并保存!"
        elif key == ord('1'):
            df.loc[df.index[current_idx], 'manual_label'] = 1
            df.to_csv(filepath, index=False, encoding='utf-8')
            status_message = "已标注为 1 并保存!"
        elif key == ord('s'):
            df.to_csv(filepath, index=False, encoding='utf-8')
            status_message = "已保存!"
        elif key == ord('q'):
            break

def main():
    parser = argparse.ArgumentParser(description="一个用于QA数据对风险进行人工标注的CLI工具。")
    parser.add_argument("input_file", type=str, help="需要标注的CSV文件路径。")
    args = parser.parse_args()

    if not WCWIDTH_AVAILABLE:
        print("警告: 'wcwidth' 库未安装 (请运行 'pip install wcwidth')。")
        print("      中英文混合文本的换行可能显示不准确。程序将继续运行。\n")

    filepath = args.input_file
    if not os.path.exists(filepath):
        print(f"错误: 文件不存在 '{filepath}'")
        sys.exit(1)

    try:
        df = pd.read_csv(filepath)
    except Exception as e:
        print(f"错误: 读取CSV文件失败: {e}")
        sys.exit(1)

    # 检查并添加 manual_label 列
    if 'manual_label' not in df.columns:
        df['manual_label'] = -1  # -1 代表未标注

    # 确定起始索引：从第一个未标注的位置开始
    unlabeled_rows = df[df['manual_label'] == -1]
    if not unlabeled_rows.empty:
        start_idx = df.index.get_loc(unlabeled_rows.index[0])
    else:
        start_idx = 0 # 如果都标完了，从头开始

    curses.wrapper(main_loop, df, filepath, start_idx)
    print("标注完成，数据已保存。")


if __name__ == "__main__":
    main() 