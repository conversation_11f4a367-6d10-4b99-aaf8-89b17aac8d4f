import os
import json
import asyncio
import aiohttp
import pandas as pd
from tqdm import tqdm
import re
from datetime import datetime, timed<PERSON><PERSON>



def parse_llm_response(raw_response_text):
    """
    安全地解析LLM返回的xml字符串。
    """
    try:
        response_text = raw_response_text.split("<think off>")[-1].split("</think>")[-1]
        reasoning_match = re.search(r"<reasoning>(.*?)</reasoning>", response_text, re.DOTALL)
        is_risky_match = re.search(r"<is_risky>(.*?)</is_risky>", response_text, re.DOTALL)
        response_classification_match = re.search(r"<response_classification>(.*?)</response_classification>", response_text, re.DOTALL)
        risk_types_match = re.search(r"<risk_types>(.*?)</risk_types>", response_text, re.DOTALL)

        if all([reasoning_match, is_risky_match, response_classification_match, risk_types_match]):
            reasoning = reasoning_match.group(1).strip()
            is_risky_str = is_risky_match.group(1).strip()
            response_classification = response_classification_match.group(1).strip()
            risk_types_str = risk_types_match.group(1).strip()

            is_risky = is_risky_str
            # is_risky = True if is_risky_str == '是' else False
            
            # risk_types = risk_types_str
            # 将逗号分隔的风险类型字符串转换为列表，以适应后续处理
            if risk_types_str:
                risk_types = [s.strip() for s in risk_types_str.split(',')]
            else:
                risk_types = []

            return {
                "is_risky": is_risky,
                "response_classification": response_classification,
                "risk_types": risk_types,
                "reasoning": reasoning,
                "raw_response_text": raw_response_text,
            }
    except Exception:
        # 捕获任何在正则匹配或处理中发生的未知错误
        pass

    # 如果解析失败，返回默认结构
    return {
        "is_risky": None,
        "response_classification": None,
        "risk_types": None,
        "reasoning": "Failed to parse LLM XML response",
        "raw_response_text": raw_response_text,
    }


async def post_request_single(session, url, data):
    """
    发送单个HTTP POST请求。
    """
    try:
        async with session.post(url, json=data, timeout=1800) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception:
        answer = ""
    return answer


async def process_single_qa(session, qa_pair, semaphore, progress_bar, enable_thinking=False):
    """
    处理单个问答对的分析任务。
    """
    async with semaphore:
        # url = "http://127.0.0.1:1025/v1/chat/completions"
        url = "http://10.24.45.213:59052/v1/chat/completions"

        prompt = qa_pair['prompt']

        messages = [{"role": "user", "content": prompt}]

        body = {
            "model": "secllm",
            "stream": False,
            "top_p": 0.95,
            "temperature": 0.6,
            "max_tokens": 16384,
            # "repetition_penalty": 1.05,
            "messages": messages,
            "chat_template_kwargs": {"enable_thinking": enable_thinking}
        }

        max_retries = 3
        llm_response = ""
        for attempt in range(max_retries):
            llm_response = await post_request_single(session, url, body)
            if llm_response:
                break
            await asyncio.sleep(1)
            
        if not llm_response:
            print(f"Error: Failed to get response from LLM for qa: {qa_pair['大模型的输入内容']}")
            progress_bar.update(1)
            return qa_pair

        analysis_result = parse_llm_response(llm_response)

        # 将分析结果与原始数据合并
        qa_pair.update(analysis_result)

        progress_bar.update(1)
        return qa_pair


async def main(data_list, enable_thinking=False):
    """
    主异步函数，用于并发处理所有分析任务。
    """
    concurrency_limit = 100
    progress_bar = tqdm(total=len(data_list), desc="Analyzing QA pairs")

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [process_single_qa(session, qa, semaphore, progress_bar, enable_thinking) for qa in data_list]
        results = await asyncio.gather(*tasks)

    progress_bar.close()
    return results




if __name__ == "__main__":
    dir = "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B-thinking_2025072220"
    
    input_file = 'S5_stage_one_output_Qwen3-235B-thinking_2025072220.csv'
    
    manual_file = "/home/<USER>/risk_analysis/data/result_0630_1735.csv"
    

    print(f"Start time: {(datetime.utcnow() + timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')}")

    print(f"Reading data from '{input_file}'...")
    try:
        df = pd.read_csv(os.path.join(dir, input_file), encoding='utf-8')
        
        manual_df = pd.read_csv(manual_file)
        # 清理列名，移除空格和括号中的内容
        manual_df.columns = [col.split(' ')[0].strip() for col in manual_df.columns]

        # 验证列名是否存在
        if '问答id' not in manual_df.columns or '是否有风险（0/1:0为无风险，1为有风险）' not in manual_df.columns:
            print(f"错误: 手动标注文件 '{manual_file}' 必须包含 '问答id' 和 '是否有风险（0/1:0为无风险，1为有风险）' 列。")
            exit()
        else:
            manual_df.rename(columns={'是否有风险（0/1:0为无风险，1为有风险）': 'manual_label'}, inplace=True)
            manual_df["label"] = manual_df["manual_label"].map({1:"是", 0:"否"})
            
            # 合并数据，只保留有手动标注的行
            eval_df = pd.merge(df, manual_df[['问答id', 'label']], on='问答id', how='inner')

    except FileNotFoundError:
        print(f"Error: Input file not found at '{input_file}'")
        exit()

    # 将DataFrame转换为字典列表以进行处理
    
    epoch_num = 10
    
    for epoch in range(epoch_num):
        err_data = eval_df[eval_df["is_risky"] != eval_df["label"]]
        if len(err_data) == 0:
            print("No error data found.")
            break
        
        print(f"Epoch {epoch+1} of {epoch_num}")
        
        err_data = err_data[["问答id", "大模型的输入内容", "生成的回答", "prompt"]]
        
        qa_data = err_data.to_dict('records')
        
        print(f"Found {len(qa_data)} QA pairs to analyze.")
        
        # print("prompt example:")
        # print(qa_data[0]['prompt'])
        
        print("Starting analysis with LLM...")
        
        loop = asyncio.get_event_loop()
        analyzed_data = loop.run_until_complete(main(qa_data, enable_thinking=True))
        
        # 将分析结果转换回DataFrame
        analyzed_df = pd.DataFrame(analyzed_data)
        
        # 将risk_types列表转换为字符串，以便CSV存储
        analyzed_df['risk_types'] = analyzed_df['risk_types'].apply(lambda x: ', '.join(x) if isinstance(x, list) else '')
        
        # 保存结果
        output_file = os.path.join(dir, input_file.replace(".csv", f"_fix2_epoch_{epoch+1}.csv"))
        analyzed_df.to_csv(output_file, index=False, encoding='utf-8')
        
        data2 = analyzed_df[['问答id', 'is_risky', 'response_classification', 'risk_types', 'reasoning', 'raw_response_text']]
        data2.to_csv(output_file.replace('.csv', '_answer.csv'), index=False, encoding='utf-8')
        
        print(f"Epoch {epoch+1} complete. Saved results to '{output_file}'.")
        
        # eval_df = pd.merge(analyzed_df, manual_df[["问答id", "label"]], on="问答id", how="left")
        
    
    print("Done.") 
    
    print(f"End time: {(datetime.utcnow() + timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')}")
    


    second_manual_dir = "/home/<USER>/risk_analysis/data/2025-07/"

    second_manual_df = []

    for file in os.listdir(second_manual_dir):
        if file.endswith(".csv"):
            try:
                second_manual_df.append(pd.read_csv(os.path.join(second_manual_dir, file), encoding='utf-8'))
            except UnicodeDecodeError as e:
                second_manual_df.append(pd.read_csv(os.path.join(second_manual_dir, file), encoding='gbk'))
            except Exception as e:
                print(f"file: {file} Error: {e}")
                continue
            
    second_manual_df = pd.concat(second_manual_df)
    second_manual_df = second_manual_df[["问答id", "label"]]
    second_manual_df = second_manual_df.drop_duplicates(subset="问答id", keep="first")
    
    
    
    first_manual_file = "/home/<USER>/risk_analysis/data/result_0630_1735.csv"


    first_manual_df = pd.read_csv(first_manual_file)
    # 清理列名，移除空格和括号中的内容
    first_manual_df.columns = [col.split(' ')[0].strip() for col in first_manual_df.columns]
    first_manual_df.rename(columns={'是否有风险（0/1:0为无风险，1为有风险）': 'manual_label'}, inplace=True)
    first_manual_df["label"] = first_manual_df["manual_label"].map({1:"是", 0:"否"})
    first_manual_df = first_manual_df[["问答id", "label"]]
    first_manual_df = first_manual_df.drop_duplicates(subset="问答id", keep="first")

    # 使用第二次的标注替换第一次的标注，第二次只标注了部分，第一次的标注是全部
    manual_df = pd.merge(first_manual_df, second_manual_df, on="问答id", how="left")
    manual_df["label"] = manual_df["label_y"].fillna(manual_df["label_x"])
    manual_df = manual_df[["问答id", "label"]]
    manual_df = manual_df.drop_duplicates(subset="问答id", keep="first")

    manual_df.to_csv("/home/<USER>/risk_analysis/data/result_0724_fix.csv", index=False, encoding='utf-8')

    print("Done.") 
    
    
        