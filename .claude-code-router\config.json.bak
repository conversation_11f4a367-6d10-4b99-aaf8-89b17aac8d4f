{"APIKEY": "your-secret-key", "PROXY_URL": "http://127.0.0.1:7890", "LOG": true, "API_TIMEOUT_MS": 600000, "Providers": [{"name": "openrouter", "api_base_url": "https://openrouter.ai/api/v1/chat/completions", "api_key": "b093a5e35de8001d4e4043dc21ac53c63c75dd445cf2bca90afed2128786614f", "models": ["qwen/qwen3-coder:free", "qwen/qwen3-235b-a22b-2507:free", "qwen/qwen3-235b-a22b-thinking-2507", "moonshotai/kimi-k2:free", "z-ai/glm-4.5-air:free", "z-ai/glm-4.5-air", "z-ai/glm-4.5", "deepseek/deepseek-chat-v3-0324:free", "deepseek/deepseek-r1-0528:free", "google/gemini-2.5-flash", "google/gemini-2.5-pro"], "transformer": {"use": ["openrouter"]}}, {"name": "gemini", "api_base_url": "https://generativelanguage.googleapis.com/v1beta/models/", "api_key": "AIzaSyDvnVxe6YSZG1cC2MBmp3aOznId2BTNMbo", "models": ["gemini-2.5-flash", "gemini-2.5-pro"], "transformer": {"use": ["gemini"]}}], "Router": {"default": "openrouter,qwen/qwen3-coder:free", "background": "openrouter,moonshotai/kimi-k2:free", "think": "gemini,gemini-2.5-pro", "longContext": "openrouter,moonshotai/kimi-k2:free", "longContextThreshold": 60000, "webSearch": "gemini,gemini-2.5-flash"}}