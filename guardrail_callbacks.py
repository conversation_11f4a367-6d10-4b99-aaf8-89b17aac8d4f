from litellm.integrations.custom_logger import CustomLogger  
from litellm._logging import verbose_proxy_logger

  
class GuardrailApplier(CustomLogger):  
    def __init__(self, **kwargs):  
        super().__init__(**kwargs)  
        verbose_proxy_logger.info(f"GuardrailApplier initialized")  
    
    async def async_pre_call_hook(self, user_api_key_dict, cache, data, call_type):  
        # 检查用户API密钥的元数据  
        verbose_proxy_logger.info(f"GuardrailApplier pre_call_hook triggered for key: {user_api_key_dict}")  
        key_metadata = getattr(user_api_key_dict, "metadata", {}) or {}  
        key_guardrails = key_metadata.get("guardrails", [])  
          
        if key_guardrails:  
            # 确保请求有metadata字段  
            if "metadata" not in data:  
                data["metadata"] = {}  
              
            # 确保请求有requester_metadata字段  
            if "requester_metadata" not in data["metadata"]:  
                data["metadata"]["requester_metadata"] = {}  
              
            # 添加guardrails到请求  
            data["metadata"]["requester_metadata"]["guardrails"] = key_guardrails  
            data["metadata"]["guardrails"] = key_guardrails  
            data["guardrails"] = key_guardrails   
            data["proxy_server_request"]["body"]["guardrails"] = key_guardrails 
            verbose_proxy_logger.info(f"GuardrailApplier success add Guardrail : {key_guardrails}")  

        verbose_proxy_logger.info(f"GuardrailApplier data info : {data}")
        
        return data

guardrail_applier = GuardrailApplier()
