import json
import requests
import argparse
import logging
import time
import re
import csv
import asyncio
import aiohttp
from tqdm.asyncio import tqdm_asyncio
from collections import defaultdict

# --- 配置常量 ---
API_URL = "http://10.5.225.21:18089/v1/chat/completions"
MODEL_NAME = "secllm-v3"
AUTH_TOKEN = "sk-z8_hiXmqYSkB1V2Yj5jjlA" # 从用户curl命令中获取
HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {AUTH_TOKEN}'
}
TEMPERATURE = 0.01
TOP_P = 0.01
REPETITION_PENALTY = 1.05
MAX_TOKENS = 5000 # 足够返回单个字母或短句
STREAM_ENABLED = False

# --- Language Name to Code Mapping ---
LANGUAGE_NAME_TO_CODE_MAP = {
    "english": "en",
    "chinese": "zh",
    "bengali": "bn",
    "german": "de",
    "spanish": "es",
    "french": "fr",
    "japanese": "ja",
    "russian": "ru",
    "swahili": "sw",
    "telugu": "te",
    "thai": "th",
    # Add other common names if they appear in datasets
    # Ensure keys are lowercase for consistent matching
}

# --- Language Specific Instructions and Prefixes (User Provided) ---
LANG_TO_INSTRUCTIONS = {
    "en": '''Solve this math problem. Give the reasoning steps before giving the final answer on the last line by itself in the format of "Answer:". Do not add anything other than the integer answer after "Answer:".\n\n{input}''',
    "bn": '''এই গণিতের সমস্যাটি সমাধান করুন। চূড়ান্ত উত্তর দেওয়ার আগে যুক্তিসম্পন্ন পদক্ষেপ প্রদান করুন। চূড়ান্ত উত্তরটি একক সংখ্যা হিসাবে "উত্তর:" এর পরে শেষ লাইনে দিন। "উত্তর:" এর পরে অন্য কিছু যুক্ত করবেন না।.\n\n{input}''',
    "de": '''Löse dieses Mathematikproblem. Gib die Schritte zur Begründung an, bevor du die endgültige Antwort in der letzten Zeile alleine im Format "Antwort:" gibst. Füge nichts anderes als die ganzzahlige Antwort nach "Antwort:" hinzu.\n\n{input}''',
    "es": '''Resuelve este problema matemático. Proporciona los pasos de razonamiento antes de dar la respuesta final en la última línea por sí misma en el formato de "Respuesta:". No añadas nada más que la respuesta entera después de "Respuesta:".\n\n{input}''',
    "fr": '''Résolvez ce problème de mathématiques. Donnez les étapes de raisonnement avant de fournir la réponse finale sur la dernière ligne elle-même dans le format de "Réponse:". N\'ajoutez rien d\'autre que la réponse entière après "Réponse:".\n\n{input}''',
    "ja": '''の数学の問題を解いてください。最終的な答えを出す前に、解答の推論過程を記述してください。そして最後の行には "答え:" の形式で答えを記述し、その後には整数の答え以外何も追加しないでください。\n\n{input}''',
    "ru": '''Решите эту математическую задачу. Объясните шаги рассуждения перед тем, как дать окончательный ответ в последней строке сам по себе в формате "Ответ:". Не добавляйте ничего, кроме целочисленного ответа после "Ответ:".\n\n{input}''',
    "sw": '''Suluhisha tatizo hili la hesabu. Toa hatua za mantiki kabla ya kutoa jibu la mwisho kwenye mstari wa mwisho peke yake katika muundo wa "Jibu:". Usiongeze chochote kingine isipokuwa jibu la integer baada ya "Jibu:".\n\n{input}''',
    "te": '''ఈ గణిత సమస్యను పరిష్కరించండి. చివరి సమాధానాన్ని ఇవ్వదానికి ముందు తర్కాత్మక అదుగులను ఇవ్వండి. చివరి పంక్తిలో మాత్రమే 'సమాధానం:' అనే ఆకారంలో చివరి సమాధానాద్ని ఇవ్వండి సమాధానం: తర్వాత పూర్ణాంక సమాధానానికి తప్పించి ఎదేనా చేర్చవద్దు.\n\n{input}''',
    "th": '''แก้ปัญหาคณิตศาสตร์นี้ ให้ให้ขั้นตอนการใช้เหตุผลก่อนที่จะให้คำตอบสุดท้ายในบรรทัดสุดท้ายโดยอยู่ในรูปแบบ "คำตอบ:" ไม่ควรเพิ่มอะไรนอกจากคำตอบที่เป็นจำนวนเต็มหลังจาก "คำตอบ:"\n\n{input}''',
    "zh": '''解决这个数学问题。在最后一行给出答案前，请提供推理步骤。最后一行应该以 "答案: " 的形式独立给出答案。在 "答案：" 后不要添加除整数答案之外的任何内容。\n\n{input}''',
}

LANG_TO_ANSWER_PREFIX = {
    "en": "Answer",
    "bn": "উত্তর",
    "de": "Antwort",
    "es": "Respuesta",
    "fr": "Réponse",
    "ja": "答え",
    "ru": "Ответ",
    "sw": "Jibu",
    "te": "సమాధానం",
    "th": "คำตอบ",
    "zh": "答案",
}

# --- MCQ Specific Language Instructions ---
# New Preamble and Suffix instructions
DEFAULT_ENGLISH_MCQ_PREAMBLE = "Given the following question and four candidate answers (A, B, C and D), choose the best answer. This question is related to network security."
LANG_TO_MCQ_PREAMBLE = {
    "en": DEFAULT_ENGLISH_MCQ_PREAMBLE,
    "zh": "针对以下网络安全相关的问题和四个候选答案（A、B、C和D），请选择最佳答案。",
    "bn": "প্রদত্ত প্রশ্ন এবং চারটি সম্ভাব্য উত্তর (A, B, C এবং D) দেওয়া হলো, সঠিক উত্তরটি নির্বাচন করুন। এই প্রশ্নটি নেটওয়ার্ক নিরাপত্তা সম্পর্কিত।",
    "de": "Gegeben sind die folgende Frage und vier Antwortmöglichkeiten (A, B, C und D). Wählen Sie die beste Antwort aus. Diese Frage bezieht sich auf Netzwerksicherheit.",
    "es": "Dada la siguiente pregunta y cuatro posibles respuestas (A, B, C y D), elija la mejor respuesta. Esta pregunta está relacionada con la seguridad de redes.",
    "fr": "Compte tenu de la question suivante et des quatre réponses possibles (A, B, C et D), choisissez la meilleure réponse. Cette question est liée à la sécurité des réseaux.",
    "ja": "以下の設問と4つの選択肢（A、B、C、D）を考慮し、最適な回答を選択してください。この質問はネットワークセキュリティに関連しています。",
    "ru": "Учитывая следующий вопрос и четыре варианта ответа (A, B, C и D), выберите лучший ответ. Этот вопрос связан с сетевой безопасностью.",
    "sw": "Kwa kuzingatia swali lifuatalo na majibu manne yanayowezekana (A, B, C na D), chagua jibu bora zaidi. Swali hili linahusiana na usalama wa mtandao.",
    "te": "ఈ క్రింది ప్రశ్న మరియు నాలుగు అభ్యర్థి సమాధానాలు (A, B, C మరియు D) ఆధారంగా, ఉత్తమ సమాధానాన్ని ఎంచుకోండి. ఈ ప్రశ్న నెట్వర్క్ భద్రతకు సంబంధించినది.",
    "th": "จากคำถามต่อไปนี้และคำตอบที่เป็นไปได้สี่ข้อ (A, B, C และ D) โปรดเลือกคำตอบที่ดีที่สุด คำถามนี้เกี่ยวข้องกับความปลอดภัยของเครือข่าย",
    "unknown": DEFAULT_ENGLISH_MCQ_PREAMBLE
}

DEFAULT_ENGLISH_MCQ_SUFFIX_FORMAT_INSTRUCTION = "Your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of A, B, C, or D."
LANG_TO_MCQ_SUFFIX_FORMAT_INSTRUCTION = {
    "en": DEFAULT_ENGLISH_MCQ_SUFFIX_FORMAT_INSTRUCTION,
    "zh": "您的回答应采用以下格式：'答案: $LETTER'（不含引号），其中LETTER是A、B、C或D中的一个。",
    "bn": "আপনার উত্তর নিম্নলিখিত বিন্যাসে হওয়া উচিত: 'উত্তর: $LETTER' (উদ্ধৃতি ছাড়া), যেখানে LETTER হলো A, B, C, বা D এর মধ্যে একটি।",
    "de": "Ihre Antwort sollte folgendem Format entsprechen: 'Antwort: $LETTER' (ohne Anführungszeichen), wobei LETTER einer der Buchstaben A, B, C oder D ist.",
    "es": "Su respuesta debe tener el siguiente formato: 'Respuesta: $LETTER' (sin comillas), donde LETTER es una de A, B, C o D.",
    "fr": "Votre réponse doit être au format suivant : 'Réponse: $LETTER' (sans guillemets), où LETTER est l'une des lettres A, B, C ou D.",
    "ja": "回答は次の形式である必要があります：'答え: $LETTER'（引用符なし）、ここでLETTERはA、B、C、またはDのいずれかです。",
    "ru": "Ваш ответ должен быть в следующем формате: 'Ответ: $LETTER' (без кавычек), где LETTER — это одна из букв A, B, C или D.",
    "sw": "Jibu lako linapaswa kuwa katika muundo ufuatao: 'Jibu: $LETTER' (bila alama za nukuu), ambapo LETTER ni moja ya A, B, C, au D.",
    "te": "మీ సమాధానం ఈ క్రింది ఆకృతిలో ఉండాలి: 'సమాధానం: $LETTER' (కోట్స్ లేకుండా), ఇక్కడ LETTER A, B, C, లేదా Dలో ఒకటి.",
    "th": "คำตอบของคุณควรอยู่ในรูปแบบต่อไปนี้: 'คำตอบ: $LETTER' (ไม่มีเครื่องหมายคำพูด) โดยที่ LETTER คือหนึ่งใน A, B, C หรือ D",
    "unknown": DEFAULT_ENGLISH_MCQ_SUFFIX_FORMAT_INSTRUCTION
}

# --- 日志设置 ---
def setup_logging(log_level=logging.INFO):
    """配置脚本的基础日志记录功能。"""
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

# --- Individual Data Parsers ---
def _parse_ctibench_mcq_data(file_path: str) -> list[dict]:
    """为 ctibench-mcq 数据集 (TSV格式) 加载并解析MCQ测试数据。"""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8', newline='') as f:
            reader = csv.reader(f, delimiter='\t')
            header = next(reader, None)
            if not header:
                logging.error(f"TSV文件 {file_path} 为空或没有表头。")
                return []
            
            try:
                normalized_header = [h.strip().lower() for h in header]
                idx_question = normalized_header.index('question')
                idx_opt_a = normalized_header.index('option a')
                idx_opt_b = normalized_header.index('option b')
                idx_opt_c = normalized_header.index('option c')
                idx_opt_d = normalized_header.index('option d')
                idx_prompt = normalized_header.index('prompt')
                idx_gt = normalized_header.index('gt')
            except ValueError:
                logging.error(
                    f"TSV文件 {file_path} 的表头与预期格式不符 "
                    f"(需要包含: 'question', 'option a', 'option b', 'option c', 'option d', 'prompt', 'gt')。"
                    f"检测到的表头: {header}"
                )
                return []

            for i, row in enumerate(reader):
                line_num = i + 2 
                required_col_count = max(idx_question, idx_opt_a, idx_opt_b, idx_opt_c, idx_opt_d, idx_prompt, idx_gt) + 1
                if len(row) < required_col_count:
                    logging.warning(f"TSV文件 {file_path} 第 {line_num} 行数据列数不足 (需要 {required_col_count} 列，实际 {len(row)} 列)，已跳过: {row}")
                    continue
                try:
                    item = {
                        'question': row[idx_question],
                        'answers': [row[idx_opt_a], row[idx_opt_b], row[idx_opt_c], row[idx_opt_d]],
                        'label': row[idx_gt].strip().upper(),
                        'language': "en", 
                        'raw_prompt': row[idx_prompt]
                    }
                    
                    if not all([item['question'], all(item['answers']), item['label']]):
                         logging.warning(f"TSV文件 {file_path} 第 {line_num} 行核心字段缺失，已跳过: {row}")
                         continue
                    if not item['label'] or not ('A' <= item['label'][0] <= 'Z'):
                        logging.warning(f"TSV文件 {file_path} 第 {line_num} 行 'label' 格式无效 ('{item['label']}'), 已跳过: {row}")
                        continue
                    if len(item['label']) > 1: 
                        item['label'] = item['label'][0]

                    data.append(item)
                except Exception as e:
                    logging.error(f"处理TSV文件 {file_path} 第 {line_num} 行时出错: {e} - 行数据: {row}")
            logging.info(f"成功从TSV文件 {file_path} 加载 {len(data)} 条测试数据。")
    except FileNotFoundError:
        logging.error(f"输入文件 {file_path} 未找到。")
        return []
    except Exception as e:
        logging.error(f"加载 ctibench-mcq (TSV) 文件 {file_path} 时发生意外错误: {e}")
        return []
    return data

def _parse_secbench_mcq_data(file_path: str) -> list[dict]:
    """为 secbench-mcq 数据集 (JSONL格式) 加载并解析MCQ测试数据。"""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                try:
                    item = json.loads(line.strip())
                    required_keys = ['question', 'answers', 'label']
                    if not all(k in item for k in required_keys):
                        logging.warning(f"JSONL文件 {file_path} 第 {i+1} 行数据缺失核心字段 (question, answers, label)，已跳过: {line.strip()}")
                        continue
                    
                    if not isinstance(item['answers'], list):
                        logging.warning(f"JSONL文件 {file_path} 第 {i+1} 行 'answers' 字段不是列表，已跳过: {line.strip()}")
                        continue
                    
                    item['label'] = str(item['label']).upper()
                    
                    raw_lang_name = item.get('language', "unknown").strip().lower()
                    resolved_lang_code = "unknown"

                    if raw_lang_name in LANG_TO_ANSWER_PREFIX:
                        resolved_lang_code = raw_lang_name
                    elif raw_lang_name in LANGUAGE_NAME_TO_CODE_MAP:
                        resolved_lang_code = LANGUAGE_NAME_TO_CODE_MAP[raw_lang_name]
                    else:
                        if raw_lang_name and raw_lang_name != "unknown":
                            logging.warning(f"JSONL文件 {file_path} 第 {i+1} 行语言 '{item.get('language')}' 无法映射到已知语言代码，将使用默认/unknown。")
                        
                    item['language'] = resolved_lang_code
                    item['raw_prompt'] = None 
                                        
                    data.append(item)
                except json.JSONDecodeError:
                    logging.error(f"解析JSONL文件 {file_path} 第 {i+1} 行JSON时出错: {line.strip()}")
            logging.info(f"成功从JSONL文件 {file_path} 加载 {len(data)} 条测试数据。")
    except FileNotFoundError:
        logging.error(f"输入文件 {file_path} 未找到。")
        return []
    except Exception as e:
        logging.error(f"加载 secbench-mcq (JSONL) 文件 {file_path} 时发生意外错误: {e}")
        return []
    return data

def _parse_ctibench_rcm_data(file_path: str) -> list[dict]:
    """为 ctibench-rcm 数据集 (TSV格式) 加载并解析数据。"""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8', newline='') as f:
            reader = csv.reader(f, delimiter='\t')
            header = next(reader, None) 
            if not header:
                logging.error(f"ctibench-rcm TSV文件 {file_path} 为空或没有表头。")
                return []
            
            try:
                normalized_header = [h.strip().lower() for h in header]
                # Expected: URL, Description, Prompt, GT
                idx_description = normalized_header.index('description')
                idx_prompt = normalized_header.index('prompt')
                idx_gt = normalized_header.index('gt')
                # URL is present but not directly used in the item structure for now
            except ValueError:
                logging.error(
                    f"ctibench-rcm TSV文件 {file_path} 的表头与预期格式不符 "
                    f"(需要包含: 'Description', 'Prompt', 'GT')。"
                    f"检测到的表头: {header}"
                )
                return []

            for i, row in enumerate(reader):
                line_num = i + 2 
                required_col_count = max(idx_description, idx_prompt, idx_gt) + 1
                if len(row) < required_col_count:
                    logging.warning(f"ctibench-rcm TSV文件 {file_path} 第 {line_num} 行数据列数不足，已跳过: {row}")
                    continue
                try:
                    item = {
                        'question': row[idx_description],
                        'answers': [], # Not an MCQ
                        'label': row[idx_gt].strip(), # Keep case for CWE IDs
                        'language': "en", 
                        'raw_prompt': row[idx_prompt],
                        'dataset_type': 'rcm' # Internal marker for RCM type tasks
                    }
                    
                    if not all([item['question'], item['label'], item['raw_prompt']]):
                         logging.warning(f"ctibench-rcm TSV文件 {file_path} 第 {line_num} 行核心字段缺失，已跳过: {row}")
                         continue
                    data.append(item)
                except Exception as e:
                    logging.error(f"处理 ctibench-rcm TSV文件 {file_path} 第 {line_num} 行时出错: {e} - 行数据: {row}")
        logging.info(f"成功从 ctibench-rcm TSV文件 {file_path} 加载 {len(data)} 条测试数据。")
    except FileNotFoundError:
        logging.error(f"输入文件 {file_path} 未找到。")
        return []
    except Exception as e:
        logging.error(f"加载 ctibench-rcm (TSV) 文件 {file_path} 时发生意外错误: {e}")
        return []
    return data

def _parse_cybermetric_data(file_path: str) -> list[dict]:
    """为 CyberMetric 数据集 (JSON格式) 加载并解析MCQ测试数据。"""
    data_items = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            loaded_json = json.load(f)
        
        if 'questions' not in loaded_json or not isinstance(loaded_json['questions'], list):
            logging.error(f"JSON文件 {file_path} 格式错误: 缺少 'questions' 列表。")
            return []

        for i, item in enumerate(loaded_json['questions']):
            line_num_equivalent = i + 1 # For logging purposes
            try:
                question_text = item['question']
                answer_dict = item['answers']
                solution = item['solution']

                if not all([question_text, isinstance(answer_dict, dict), solution]):
                    logging.warning(f"CyberMetric文件 {file_path} 第 {line_num_equivalent} 条数据核心字段缺失或类型错误，已跳过: {item}")
                    continue

                # Convert answer_dict to answer_list, ordered by keys (A, B, C, D)
                # Assuming standard A, B, C, D options. Modify if more flexible ordering is needed.
                answer_list = []
                expected_keys = sorted(answer_dict.keys()) # Sorts 'A', 'B', 'C', 'D'
                
                # Validate that keys are single uppercase letters as expected for MCQ choices
                valid_answer_keys = True
                for key in expected_keys:
                    if not (len(key) == 1 and 'A' <= key.upper() <= 'Z'):
                        valid_answer_keys = False
                        break
                
                if not valid_answer_keys:
                    logging.warning(f"CyberMetric文件 {file_path} 第 {line_num_equivalent} 条数据的答案键格式无效，已跳过: {answer_dict}")
                    continue
                
                for key in expected_keys:
                    answer_list.append(answer_dict[key])

                if not answer_list:
                     logging.warning(f"CyberMetric文件 {file_path} 第 {line_num_equivalent} 条数据答案列表为空，已跳过: {item}")
                     continue


                label = str(solution).strip().upper()
                if not (len(label) == 1 and 'A' <= label <= 'Z'): # Basic validation for label
                    logging.warning(f"CyberMetric文件 {file_path} 第 {line_num_equivalent} 条数据 'solution' 格式无效 ('{label}'), 已跳过: {item}")
                    continue

                parsed_item = {
                    'question': question_text,
                    'answers': answer_list,
                    'label': label,
                    'language': "en",
                    'raw_prompt': None, # Can be generated by format_mcq_prompt
                    'dataset_type': 'mcq' # Explicitly mark as MCQ for any logic that might use it
                }
                data_items.append(parsed_item)
            except KeyError as e:
                logging.error(f"处理CyberMetric文件 {file_path} 第 {line_num_equivalent} 条数据时缺少键: {e} - 数据: {item}")
            except Exception as e:
                logging.error(f"处理CyberMetric文件 {file_path} 第 {line_num_equivalent} 条数据时发生意外错误: {e} - 数据: {item}")
        
        logging.info(f"成功从CyberMetric文件 {file_path} 加载 {len(data_items)} 条测试数据。")

    except FileNotFoundError:
        logging.error(f"输入文件 {file_path} 未找到。")
        return []
    except json.JSONDecodeError as e:
        logging.error(f"解析CyberMetric JSON文件 {file_path} 时出错: {e}")
        return []
    except Exception as e:
        logging.error(f"加载CyberMetric文件 {file_path} 时发生意外错误: {e}")
        return []
    return data_items

# --- 数据加载 (主调度函数) ---
def load_mcq_data(file_path: str, dataset_name: str) -> list[dict]:
    """根据指定的数据集名称从文件加载MCQ测试数据，调度到特定的解析函数。"""
    if dataset_name == 'ctibench-mcq': 
        return _parse_ctibench_mcq_data(file_path)
    elif dataset_name == 'secbench-mcq': 
        return _parse_secbench_mcq_data(file_path)
    elif dataset_name == 'ctibench-rcm':
        return _parse_ctibench_rcm_data(file_path)
    elif dataset_name == 'cybermetric':
        return _parse_cybermetric_data(file_path)
    else:
        logging.error(f"不支持的数据集名称: {dataset_name}。请使用 'secbench-mcq', 'ctibench-mcq', 'ctibench-rcm', 或 'cybermetric'。")
        return []

# --- 提示格式化 ---
def format_mcq_prompt(question_text: str, answer_choices: list[str], language_code: str = "en") -> str:
    """为MCQ问题构建提示字符串。"""
    
    preamble = LANG_TO_MCQ_PREAMBLE.get(language_code, DEFAULT_ENGLISH_MCQ_PREAMBLE)
    suffix_instruction = LANG_TO_MCQ_SUFFIX_FORMAT_INSTRUCTION.get(language_code, DEFAULT_ENGLISH_MCQ_SUFFIX_FORMAT_INSTRUCTION)

    formatted_choices_string = ""
    for i, choice in enumerate(answer_choices):
        label = chr(ord('A') + i)
        formatted_choices_string += f"{label}. {choice}\\n"
    
    # Construct the prompt using preamble, question, choices, and suffix instruction
    prompt = f"{preamble}\\n\\n{question_text}\\n{formatted_choices_string.strip()}\\n\\n{suffix_instruction}"
    return prompt

# --- API 调用相关 --- 

async def async_api_request_single(session: aiohttp.ClientSession, url: str, headers: dict, payload: dict, timeout_seconds: int = 180) -> tuple[str | None, str | None]:
    """发送单个异步API POST请求并返回响应内容或错误信息。"""
    try:
        async with session.post(url, json=payload, headers=headers, timeout=aiohttp.ClientTimeout(total=timeout_seconds)) as response:
            response_text_content = await response.text() # Read text first for better error logging
            if response.status == 200:
                try:
                    response_data = json.loads(response_text_content) # Use json.loads as response.json() might have issues with content type
                    if response_data.get("choices") and isinstance(response_data["choices"], list) and len(response_data["choices"]) > 0:
                        message = response_data["choices"][0].get("message")
                        if message and isinstance(message, dict) and "content" in message:
                            extracted_text = message.get("content")
                            return extracted_text.strip() if extracted_text else "", None
                        else:
                            error_msg = "API响应的 'choices[0]' 中缺少 'message' 或 'content'。"
                            logging.error(f"{error_msg} 完整响应: {response_data}")
                            return None, error_msg
                    else:
                        error_msg = "API响应缺少 'choices' 数组或数组为空。"
                        logging.error(f"{error_msg} 完整响应: {response_data}")
                        return None, error_msg
                except json.JSONDecodeError as e:
                    error_msg = f"解码JSON响应失败: {e}。响应文本: {response_text_content[:200]}"
                    logging.error(error_msg)
                    return None, error_msg
            else:
                error_msg = f"API请求失败，状态码: {response.status}。响应: {response_text_content[:200]}"
                logging.error(error_msg)
                return None, error_msg
    except asyncio.TimeoutError:
        error_msg = f"API请求超时 ({timeout_seconds}秒)。URL: {url}"
        logging.error(error_msg)
        return None, error_msg
    except aiohttp.ClientError as e:
        error_msg = f"API请求因网络或客户端错误失败: {e}。URL: {url}"
        logging.error(error_msg)
        return None, error_msg
    except Exception as e:
        error_msg = f"调用API时发生意外错误: {e}。URL: {url}"
        logging.error(error_msg)
        return None, error_msg

# --- 标签提取 ---
def extract_label_from_response(response_text: str, language_code: str = "en") -> str | None:
    """从模型响应中提取单个字母标签 (A, B, C, D等)。"""
    if not response_text:
        return None
    
    original_text_for_logging = response_text # For logging before modifications
    text_upper = response_text.strip().upper()

    # 1. 直接匹配单个大写字母 A-H
    if len(text_upper) == 1 and 'A' <= text_upper <= 'H':
        return text_upper

    # 2. 匹配如 "A.", "A)", "(A)" (确保只匹配开头的字母)
    match = re.match(r'^([A-H])(?:[\\.\\)\\s]|$)', text_upper)
    if match:
        return match.group(1)

    # 3. 匹配特定语言的答案前缀, 例如 "Answer: A", "答案: A"
    answer_prefix = LANG_TO_ANSWER_PREFIX.get(language_code)
    effective_text_for_prefix_match = response_text.strip() # Use original case for prefix matching, then upper for extracted letter

    if answer_prefix:
        # For Chinese, also specifically look for '选' or '选择' before the letter.
        # These are often used without the standard prefix "答案".
        if language_code == "zh":
            # Pattern: (答案|选|选择) followed by optional space/colon, then the letter A-H
            # We match against effective_text_for_prefix_match which has original casing for prefixes.
            zh_pattern = rf'(?:{re.escape(answer_prefix)}[：:]?\s*|选|选择)\s*([A-Ha-h])'
            match = re.search(zh_pattern, effective_text_for_prefix_match)
        else:
            pattern = rf'{re.escape(answer_prefix)}[：:]?\s*([A-Ha-h])'
            match = re.search(pattern, effective_text_for_prefix_match, re.IGNORECASE) # IGNORECASE for prefix in other languages
            
        if match:
            return match.group(1).upper()
    
    # 4. 如果整个文本以 A-H 中的一个字母开头 (作为后备), and is not part of a longer word
    if text_upper and 'A' <= text_upper[0] <= 'H':
        if len(text_upper) == 1 or (len(text_upper) > 1 and not text_upper[1].isalpha()):
            return text_upper[0]
        
    logging.warning(f"[{language_code}] 无法从响应中提取有效标签: '{original_text_for_logging}'")
    return None

def extract_cwe_label_from_response(response_text: str) -> str | None:
    """从模型响应中提取CWE标签。

    尝试在响应的最后非空行中查找 r'CWE-\d+' 模式。
    对 'cwe-' 前缀的大小写不敏感，并会将提取的标签规范化为大写 'CWE-'。
    如果找不到该模式，则返回最后非空行作为后备，并记录警告。
    """
    if not response_text:
        return None
    lines = response_text.strip().splitlines()
    if not lines:
        logging.warning(f"无法从空响应或仅含空白的响应中提取CWE标签: '{response_text}'")
        return None
    
    last_non_empty_line = ""
    for line in reversed(lines):
        stripped_line = line.strip()
        if stripped_line:
            last_non_empty_line = stripped_line
            break # Found the last non-empty line
            
    if not last_non_empty_line:
        logging.warning(f"响应中未找到非空行以提取CWE标签: '{response_text}'")
        return None

    # 尝试在最后非空行中查找 'CWE-' 后跟数字的模式 (例如, 'CWE-20')
    # re.IGNORECASE 使其能匹配 'cwe-20', 'Cwe-20' 等
    match = re.search(r'CWE-\d+', last_non_empty_line, re.IGNORECASE)
    
    if match:
        extracted_cwe_id = match.group(0).upper() # .group(0) 是整个匹配项, .upper() 确保 'CWE-' 是大写
        # 如果提取的ID与原始的最后非空行不同，说明可能进行了一些清理或提取操作
        if extracted_cwe_id != last_non_empty_line:
            logging.info(f"从行 '{last_non_empty_line}' 中成功提取并规范化CWE ID为: '{extracted_cwe_id}'")
        else:
            logging.info(f"成功提取CWE ID: '{extracted_cwe_id}' (与最后非空行完全匹配)")
        return extracted_cwe_id
    else:
        # 如果没有找到 'CWE-\d+' 模式，则记录警告并返回原始的最后非空行
        logging.warning(
            f"在最后非空行 '{last_non_empty_line}' 中未找到 'CWE-\\d+' 模式。"
            f"将使用此原始行作为预测标签，请检查其是否符合预期。原始完整响应: '{response_text[:500]}...'")
        return last_non_empty_line

# --- 主执行函数 ---
def main():
    """运行MCQ评测脚本的主函数。"""
    setup_logging()

    MCQ_DATASET_NAMES = {'secbench-mcq', 'ctibench-mcq', 'cybermetric'} 

    parser = argparse.ArgumentParser(description="MCQ评测脚本，用于调用聊天API并评估准确率。")
    parser.add_argument(
        "--input_file", 
        type=str, 
        required=True, 
        nargs='+',
        help="一个或多个输入测试数据文件的路径 (例如: file1.jsonl file2.tsv)。所有文件将使用相同的 --dataset_name 解析。"
    )
    parser.add_argument(
        "--dataset_name",
        type=str,
        required=True,
        choices=['secbench-mcq', 'ctibench-mcq', 'ctibench-rcm', 'cybermetric'],
        help="指定评测数据集的名称。可选值: 'secbench-mcq' (JSONL), 'ctibench-mcq' (TSV), 'ctibench-rcm' (TSV), 'cybermetric' (JSON)."
    )
    parser.add_argument(
        "--output_file", 
        type=str, 
        default=None, 
        help="保存详细结果的输出文件路径 (可选)。"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用DEBUG级别日志记录。"
    )
    parser.add_argument(
        "--concurrency",
        type=int,
        default=30,
        help="API请求的并发限制数。默认值: 30。"
    )
    args = parser.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    logging.info("启动评测脚本...")

    # A. 收集API和模型配置
    model_config = {
        "MODEL_NAME": MODEL_NAME,
        "TEMPERATURE": TEMPERATURE,
        "TOP_P": TOP_P,
        "REPETITION_PENALTY": REPETITION_PENALTY,
        "MAX_TOKENS": MAX_TOKENS,
        "STREAM_ENABLED": STREAM_ENABLED 
    }
    
    # 加载所有数据
    raw_loaded_data = []
    if args.input_file:
        for file_path_item in args.input_file:
            logging.info(f"正在从文件加载数据: {file_path_item} (数据集: {args.dataset_name})" )
            current_file_data = load_mcq_data(file_path_item, args.dataset_name)
            if current_file_data:
                raw_loaded_data.extend(current_file_data)
            else:
                logging.warning(f"未能从文件 {file_path_item} 加载任何数据或文件为空。")
    
    if not raw_loaded_data:
        logging.error("未能从任何输入文件加载测试数据。正在退出。")
        return

    # 初始化统计变量
    results_by_dataset = defaultdict(lambda: defaultdict(lambda: {'correct': 0, 'total': 0}))
    overall_correct = 0
    # overall_total 将基于实际尝试处理（包括预处理失败）的项目数
    #  len(raw_loaded_data) 是一个好的基准，每个项目都会被尝试预处理
    overall_total_items_for_accuracy_calc = len(raw_loaded_data) 
    api_errors = 0 # 包括预处理中确定无法调用API的错误 + API调用本身的错误
    total_request_time = 0.0
    processed_cases_count = 0 # 实际处理或尝试处理的案例数 (包括预处理失败)

    output_file_handle = None
    if args.output_file:
        try:
            output_file_handle = open(args.output_file, 'w', encoding='utf-8')
            logging.info(f"结果将保存到: {args.output_file}")
            output_file_handle.write("--- 评测详细结果 ---\\n\\n")
        except IOError as e:
            logging.error(f"无法打开输出文件 {args.output_file}: {e}。结果将不会保存到文件。")
            output_file_handle = None

    # B. 预处理测试数据 (生成 prompt_to_use 并过滤)
    valid_items_for_api = []
    logging.info(f"开始预处理 {len(raw_loaded_data)} 个已加载的测试用例...")

    for i, test_case_item in enumerate(raw_loaded_data):
        case_number = i + 1
        test_case_item['original_case_number'] = case_number # 用于日志和结果追溯
        
        question = test_case_item.get("question")
        answers = test_case_item.get("answers")
        true_label = test_case_item.get("label")
        language = test_case_item.get("language", "unknown")
        dataset_type = test_case_item.get("dataset_type") # 'rcm' or None

        # 预处理日志条目准备
        preprocess_log_parts = [
            f"--- 预处理测试用例 {case_number} ---",
            f"数据集: {args.dataset_name}",
            f"原始数据: {str(test_case_item)[:200]}..."
        ]

        # 核心字段验证
        if not question or not true_label:
            logging.warning(f"预处理: 测试用例 {case_number} ({args.dataset_name}) 数据不完整 (question或label缺失)，已跳过API调用。")
            results_by_dataset[args.dataset_name][language]['total'] += 1 # 计入总数，但不计入正确
            processed_cases_count +=1
            if output_file_handle: preprocess_log_parts.append("错误: Question或Label缺失，跳过API调用。")
            # continue # Don't add to valid_items_for_api
        elif args.dataset_name in MCQ_DATASET_NAMES and (not isinstance(answers, list) or not answers):
            logging.warning(f"预处理: MCQ测试用例 ({args.dataset_name}) {case_number} 的 'answers' 为空或不是列表，已跳过API调用。")
            results_by_dataset[args.dataset_name][language]['total'] += 1
            processed_cases_count +=1
            if output_file_handle: preprocess_log_parts.append("错误: MCQ 'answers' 字段无效，跳过API调用。")
            # continue
        else:
            # 提示确定逻辑
            prompt_to_use = ""
            raw_prompt_text = test_case_item.get("raw_prompt")
            if raw_prompt_text and raw_prompt_text.strip():
                prompt_to_use = raw_prompt_text
            elif args.dataset_name in MCQ_DATASET_NAMES:
                prompt_to_use = format_mcq_prompt(question, answers, language)
            else: # 非MCQ类型必须有raw_prompt
                logging.error(f"预处理: 非MCQ测试 ({args.dataset_name}) {case_number} 缺少raw_prompt，无法继续，已跳过API调用。")
                api_errors += 1
                results_by_dataset[args.dataset_name][language]['total'] += 1
                processed_cases_count +=1
                if output_file_handle: preprocess_log_parts.append("错误: 非MCQ类型缺少raw_prompt，跳过API调用。")
                # continue

            if not prompt_to_use:
                logging.error(f"预处理: 测试用例 {case_number} ({args.dataset_name}) 最终提示为空，已跳过API调用。")
                api_errors += 1 # Treat as an error preventing API call
                results_by_dataset[args.dataset_name][language]['total'] += 1
                processed_cases_count +=1
                if output_file_handle: preprocess_log_parts.append("错误: 最终提示为空，跳过API调用。")
                # continue
            else:
                test_case_item['prompt_to_use'] = prompt_to_use
                valid_items_for_api.append(test_case_item)
                # No processed_cases_count increment here, it will be counted after API call attempt

        # Write pre-processing skip log if item was skipped
        if test_case_item not in valid_items_for_api and output_file_handle:
             log_entry_str = "\\n".join(preprocess_log_parts) + "\\n\\n"
             try: output_file_handle.write(log_entry_str) 
             except IOError as e: logging.error(f"写入预处理跳过日志时出错: {e}")
    
    logging.info(f"预处理完成。{len(valid_items_for_api)} 个测试用例有效，准备进行API调用。")
    if len(raw_loaded_data) - len(valid_items_for_api) > 0:
        logging.info(f"{len(raw_loaded_data) - len(valid_items_for_api)} 个测试用例在预处理阶段被跳过。")

    # C. 调用异步处理
    api_call_results = []
    if valid_items_for_api:
        logging.info(f"开始对 {len(valid_items_for_api)} 个有效测试用例进行API调用 (并发数: {args.concurrency})...")
        api_call_results = asyncio.run(async_run_evaluations(valid_items_for_api, API_URL, HEADERS, model_config, args.concurrency))
    else:
        logging.info("没有有效的测试用例可发送到API。")

    # D. 后处理API调用结果
    logging.info(f"API调用完成。开始后处理 {len(api_call_results)} 个结果...")
    for api_result in api_call_results:
        processed_cases_count += 1 # Count items that went through API processing stage

        original_case_item = api_result['original_case_data']
        case_number = original_case_item['original_case_number']
        model_response_text = api_result['response_text']
        error_msg_from_api = api_result['error_msg']
        duration = api_result['duration']

        question = original_case_item.get("question")
        answers = original_case_item.get("answers") # May be empty for RCM
        true_label = original_case_item.get("label")
        language = original_case_item.get("language", "unknown")
        # dataset_type = original_case_item.get("dataset_type") # Not strictly needed if using args.dataset_name

        if duration: # Duration might be None if prompt was missing before API call stage in async_process_item
             total_request_time += duration

        log_entry_parts = [
            f"--- 测试用例 {case_number} ---",
            f"数据集: {args.dataset_name}",
            f"语言: {language}",
            f"问题/描述: {question}",
        ]
        if answers: # Only print for MCQ
            for idx, ans_text in enumerate(answers):
                log_entry_parts.append(f"  {chr(ord('A') + idx)}. {ans_text}")
        log_entry_parts.append(f"真实答案/标签: {true_label}")

        predicted_label = None
        is_correct = False

        if error_msg_from_api:
            api_errors += 1
            log_entry_parts.append(f"API错误: {error_msg_from_api}")
        elif model_response_text is not None:
            log_entry_parts.append(f"模型原始响应: {model_response_text}")
            if args.dataset_name == 'ctibench-rcm':
                predicted_label = extract_cwe_label_from_response(model_response_text)
            elif args.dataset_name in MCQ_DATASET_NAMES:
                predicted_label = extract_label_from_response(model_response_text, language)
            else:
                logging.warning(f"数据集 {args.dataset_name} 特定标签提取逻辑未定义，尝试默认MCQ提取。")
                predicted_label = extract_label_from_response(model_response_text, language)
            
            log_entry_parts.append(f"提取的预测答案/标签: {predicted_label if predicted_label else 'N/A'}")
            if predicted_label and true_label:
                is_correct = (predicted_label.strip().lower() == true_label.strip().lower())
        else:
             log_entry_parts.append("模型响应: 无 (API调用成功但无内容，或预处理跳过API调用)")
             if not error_msg_from_api: # If no specific API error, mark as a generic API error for stat
                api_errors +=1 

        log_entry_parts.append(f"是否正确: {'是' if is_correct else '否'}")
        log_entry_parts.append(f"请求耗时 (约): {duration:.2f}秒" if duration is not None else "请求耗时: N/A")
        
        # 更新统计 (total for this dataset/lang was already incremented in pre-processing or if it's a new item here)
        # Ensure every item that reaches here has its total counted if not pre-skipped
        # This check is slightly redundant if all pre-skipped items correctly incremented totals, 
        # but acts as a safeguard. This logic path is for items that ATTEMPTED an API call.
        if original_case_item in valid_items_for_api: # It means it wasn't pre-skipped
            results_by_dataset[args.dataset_name][language]['total'] += 1

        if is_correct:
            results_by_dataset[args.dataset_name][language]['correct'] += 1
            overall_correct += 1
        
        log_entry_str = "\\n".join(log_entry_parts) + "\\n\\n"
        if output_file_handle:
            try: output_file_handle.write(log_entry_str)
            except IOError as e: logging.error(f"写入输出文件时出错: {e}")
        logging.info(f"测试用例 {case_number} ({args.dataset_name}) 处理完毕。正确: {'是' if is_correct else '否'}")

    logging.info("--- 测试运行结束 ---")

    # E. 最终摘要计算和打印
    summary_lines = [
        "\\n--- 测试摘要 ---",
        # Use len(raw_loaded_data) for total items considered from files
        f"从文件加载并尝试处理的测试用例总数: {len(raw_loaded_data)}", 
        f"成功通过预处理并尝试API调用的案例数: {len(valid_items_for_api)}",
        f"API调用错误数 (包括预处理确定无法调用和实际API调用失败): {api_errors}",
    ]

    # Calculate effective processed cases for time calculation (those that made it to API call stage)
    # This is len(api_call_results) or len(valid_items_for_api)
    effective_api_calls_attempted = len(api_call_results)
    if effective_api_calls_attempted > 0:
         # total_request_time only includes durations from successful or attempted API calls
         valid_api_processed_count = effective_api_calls_attempted - sum(1 for res in api_call_results if res['error_msg'] and res['response_text'] is None)
         if total_request_time > 0 and valid_api_processed_count > 0: # ensure no division by zero
            summary_lines.append(f"总API请求耗时 (有效调用): {total_request_time:.2f}秒")
            avg_request_time = total_request_time / valid_api_processed_count 
            summary_lines.append(f"平均请求耗时 (有效调用): {avg_request_time:.2f}秒")
         else:
            summary_lines.append("总API请求耗时: N/A (无成功API调用或时间记录)")
            summary_lines.append("平均请求耗时: N/A (无成功API调用或时间记录)") 
    else:
        summary_lines.append("总API请求耗时: N/A (无API调用尝试)")
        summary_lines.append("平均请求耗时: N/A (无API调用尝试)")

    summary_lines.append("\\n准确率 (ACC) 按数据集和语言统计:")
    grand_total_cases_for_accuracy = 0
    grand_total_correct_for_accuracy = 0

    for ds_name, lang_stats in results_by_dataset.items():
        summary_lines.append(f"  数据集: {ds_name}")
        dataset_total_correct_summary = 0
        dataset_total_cases_summary = 0
        for lang, stats in lang_stats.items():
            lang_acc = (stats['correct'] / stats['total'] * 100) if stats['total'] > 0 else 0.0
            summary_lines.append(f"    - {lang}: {lang_acc:.2f}% ({stats['correct']}/{stats['total']})")
            dataset_total_correct_summary += stats['correct']
            dataset_total_cases_summary += stats['total']
        
        # Add dataset-level summary if multiple languages or for clarity
        if dataset_total_cases_summary > 0:
            ds_acc_summary = (dataset_total_correct_summary / dataset_total_cases_summary * 100)
            summary_lines.append(f"    - {ds_name} (汇总): {ds_acc_summary:.2f}% ({dataset_total_correct_summary}/{dataset_total_cases_summary})")
        grand_total_cases_for_accuracy += dataset_total_cases_summary
        grand_total_correct_for_accuracy += dataset_total_correct_summary

    # Use grand_total_cases_for_accuracy which sums up all stats['total'] for the final accuracy.
    # This should align with overall_total_items_for_accuracy_calc if all items were categorized. 
    # Fallback to overall_total_items_for_accuracy_calc if grand_total_cases_for_accuracy is zero but items were loaded.
    final_denominator = grand_total_cases_for_accuracy if grand_total_cases_for_accuracy > 0 else overall_total_items_for_accuracy_calc
    if final_denominator > 0:
        overall_acc = (grand_total_correct_for_accuracy / final_denominator * 100)
        summary_lines.append(f"\\n总体准确率 (ACC): {overall_acc:.2f}% ({grand_total_correct_for_accuracy}/{final_denominator})")
    else:
        summary_lines.append("\\n总体准确率 (ACC): N/A (无测试用例计入统计)")
    
    summary_str = "\\n".join(summary_lines)
    logging.info(summary_str)

    if output_file_handle:
        try:
            output_file_handle.write("\\n" + summary_str + "\\n")
            logging.info(f"摘要也已写入 {args.output_file}")
        except IOError as e:
            logging.error(f"写入摘要到输出文件时出错: {e}")
        finally:
            output_file_handle.close()
            logging.info(f"输出文件 {args.output_file} 已关闭。")

    logging.info("评测脚本执行完毕。")

async def async_process_item_api_call(
    session: aiohttp.ClientSession, 
    test_case_item: dict, 
    api_url: str, 
    api_headers: dict, 
    model_config: dict, # Contains MODEL_NAME, TEMPERATURE, etc.
    semaphore: asyncio.Semaphore, 
    max_retries: int = 3,
    # progress_bar: tqdm_asyncio | None = None # Added for explicit passing if not using tqdm_asyncio.gather
) -> dict:
    """处理单个测试用例的API调用，包括重试逻辑。"""
    async with semaphore:
        prompt_to_use = test_case_item.get('prompt_to_use') # Assume this is pre-populated
        if not prompt_to_use:
            logging.error(f"测试用例 {test_case_item.get('original_case_number', 'N/A')} 缺少 'prompt_to_use'，无法调用API。")
            return {
                'original_case_number': test_case_item.get('original_case_number'),
                'response_text': None, 
                'error_msg': "Missing prompt_to_use for API call", 
                'duration': 0.0
            }

        payload = {
            "model": model_config.get("MODEL_NAME"),
            "messages": [{"role": "user", "content": prompt_to_use}],
            "temperature": model_config.get("TEMPERATURE"),
            "top_p": model_config.get("TOP_P"),
            "repetition_penalty": model_config.get("REPETITION_PENALTY"),
            "max_tokens": model_config.get("MAX_TOKENS"),
            "stream": model_config.get("STREAM_ENABLED", False) # Default to False
        }

        answer_text = None
        error_info = None
        start_time = time.time() # For approximate duration of all retries for this item

        for attempt in range(max_retries):
            logging.debug(f"测试用例 {test_case_item.get('original_case_number', 'N/A')}: API调用尝试 {attempt + 1}/{max_retries}")
            answer_text, error_info = await async_api_request_single(session, api_url, api_headers, payload)
            if answer_text is not None: # Success
                break
            if attempt < max_retries - 1:
                logging.warning(f"测试用例 {test_case_item.get('original_case_number', 'N/A')}: API调用尝试 {attempt + 1} 失败 ({error_info})。将在1秒后重试...")
                await asyncio.sleep(1) # Wait 1 second before retrying
            else:
                logging.error(f"测试用例 {test_case_item.get('original_case_number', 'N/A')}: API调用在 {max_retries} 次尝试后均失败。最后错误: {error_info}")
        
        duration = time.time() - start_time

        # if progress_bar:
        #     progress_bar.update(1)

        return {
            'original_case_number': test_case_item.get('original_case_number'), # Pass along an identifier
            'original_case_data': test_case_item, # Pass along the original item for later processing
            'response_text': answer_text, 
            'error_msg': error_info, 
            'duration': duration
        }

async def async_run_evaluations(
    valid_test_items_with_prompts: list[dict], 
    api_url: str, 
    api_headers: dict, 
    model_config: dict, 
    concurrency_limit: int
) -> list[dict]:
    """使用并发控制异步运行所有测试用例的API调用。"""
    if not valid_test_items_with_prompts:
        logging.info("没有有效的测试用例需要通过API处理。")
        return []

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = []
        for item in valid_test_items_with_prompts:
            task = async_process_item_api_call(
                session, 
                item, 
                api_url, 
                api_headers, 
                model_config, 
                semaphore
            )
            tasks.append(task)
        
        results = await tqdm_asyncio.gather(
            *tasks, 
            desc=f"Processing {len(valid_test_items_with_prompts)} API calls (concurrency: {concurrency_limit})", 
            total=len(valid_test_items_with_prompts)
        )
        return results

if __name__ == "__main__":
    main() 