import re
import csv
import logging
from typing import Any, Dict, List, Literal, Optional, Union, AsyncGenerator, Set, Tuple
import asyncio

import litellm
from litellm._logging import verbose_proxy_logger as litellm_logger
from litellm.caching.caching import DualCache
from litellm.integrations.custom_guardrail import CustomGuardrail
from litellm.proxy._types import UserAPIKeyAuth
from litellm.proxy.guardrails.guardrail_helpers import should_proceed_based_on_metadata
from litellm.types.guardrails import GuardrailEventHooks
from litellm.types.utils import ModelResponseStream

# 创建自定义logger
logger = logging.getLogger("sensitive_word_guardrail")
logger.setLevel(logging.DEBUG)
formatter = logging.Formatter(fmt='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
                             datefmt='%m/%d/%Y %H:%M:%S')
# 控制台handler
console = logging.StreamHandler()
console.setLevel(logging.INFO)
console.setFormatter(formatter)
# 确保不会重复添加handler
if not logger.handlers:
    logger.addHandler(console)


class SensitiveWordGuardrail(CustomGuardrail):
    # 敏感词类型映射
    SENSITIVE_TYPE_MAP = {
        "0": "不做划分",
        "1": "煽动颠覆国家政权、推翻社会主义制度",
        "2": "危害国家安全和利益、损害国家形象",
        "3": "煽动分裂国家、破坏国家统一和社会稳定",
        "4": "宣扬恐怖主义、极端主义",
        "5": "宣扬民族仇恨",
        "6": "宣扬暴力、淫秽色情",
        "7": "传播虚假有害信息",
        "8": "其他法律、行政法规禁止的内容",
        "9": "民族歧视内容",
        "10": "信仰歧视内容",
        "11": "国别歧视内容",
        "12": "地域歧视内容",
        "13": "性别歧视内容",
        "14": "年龄歧视内容",
        "15": "职业歧视内容",
        "16": "健康歧视内容",
        "17": "其他方面歧视内容",
        "18": "侵犯他人其他合法权益",
        "19": "侵害他人个人信息权益",
        "20": "侵害他人名誉权",
        "21": "侵害他人荣誉权",
        "22": "侵害他人肖像权",
        "23": "侵害他人隐私权",
        "24": "危害他人身心健康",
        "25": "利用算法、数据、平台等优势，实施垄断和不正当竞争行为",
        "26": "其他商业违法违规行为",
        "27": "侵犯他人知识产权",
        "28": "违反商业道德",
        "29": "泄露他人商业秘密"
    }

    # 常见的正则表达式特殊字符
    REGEX_SPECIAL_CHARS = set('[](){}*+?.\\|^$')

    def __init__(
        self,
        sensitive_words: List[str] = None,
        case_sensitive: bool = False,
        use_regex: bool = False,
        delay_chunks: int = 10,  # 流式处理中延迟处理的chunk数量
        sensitive_words_file: Optional[str] = None,
        sensitive_words_csv: Optional[str] = None,  # 添加CSV文件路径参数
        encoding: str = "utf-8",
        default_on: bool = False, # 此Guardrail是否默认启用
        auto_detect_regex: bool = False, # 自动检测正则表达式格式的敏感词
        **kwargs,
    ):
        """
        基于敏感词表的过滤围栏
        
        Args:
            sensitive_words: 敏感词列表
            case_sensitive: 是否区分大小写
            use_regex: 是否将敏感词作为正则表达式处理
            delay_chunks: 流式输出时延迟处理的chunk数量，用于检测跨chunk的敏感词
            sensitive_words_file: 敏感词文件路径
            sensitive_words_csv: 敏感词CSV文件路径，格式为"敏感词,类型编号"
            encoding: 敏感词文件编码
            default_on: 此 Guardrail 是否默认启用。默认为 False。
            auto_detect_regex: 自动检测并处理正则表达式格式的敏感词
        """
        logger.info("开始初始化敏感词围栏...")
        
        # 初始化敏感词字典 {敏感词: (类型编号, 是否为正则表达式)}
        self.sensitive_words_dict = {}
        
        # 初始化基本敏感词 - 设置默认敏感词和类型
        default_sensitive_words = {
            "海洛因": "6", "冰毒": "6", "摇头丸": "6", "大麻": "6", 
            "可卡因": "6", "鸦片": "6", "吗啡": "6"
        }
        
        # 如果提供了敏感词列表，添加到字典中（默认类型为0）
        if sensitive_words:
            for word in sensitive_words:
                self.sensitive_words_dict[word] = ("0", False)  # (类型编号, 是否为正则表达式)
        
        # 添加默认敏感词
        for word, type_code in default_sensitive_words.items():
            self.sensitive_words_dict[word] = (type_code, False)
        
        if not sensitive_words_csv:
            sensitive_words_csv = "/home/<USER>/litellm/sensitive_words_raw.csv" # 注释掉硬编码路径
            # logger.info(f"未提供 sensitive_words_csv 参数，将不加载CSV敏感词列表。如需加载，请指定路径。")
        
        self.case_sensitive = case_sensitive
        self.use_regex = use_regex
        self.auto_detect_regex = auto_detect_regex
        self.delay_chunks = max(1, delay_chunks)  # 确保至少为1
        self.compiled_patterns = []
        
        # 存储其他参数
        self.optional_params = kwargs
        
        # 加载敏感词
        self._load_sensitive_words(sensitive_words_file, encoding)
        
        # 加载CSV格式敏感词
        self._load_csv_sensitive_words(sensitive_words_csv, encoding)
        
        # 提取敏感词集合用于编译模式
        self.sensitive_words = set(self.sensitive_words_dict.keys())
        
        # 预编译正则表达式以提高性能
        if self.sensitive_words:
            self._compile_patterns()
            logger.info(f"已编译 {len(self.compiled_patterns)} 个敏感词模式，共包含 {len(self.sensitive_words_dict)} 个有效敏感词。")
        else:
            logger.warning("敏感词列表为空（未通过参数、文件或CSV加载任何有效敏感词），围栏将不会阻止任何内容。")
        
        # 设置默认启用状态和名称
        self.default_on = default_on
        self.guardrail_name = kwargs.get("guardrail_name", "sensitive_word_guardrail")
        
        logger.info(f"敏感词围栏初始化完成: 名称='{self.guardrail_name}', 默认启用={self.default_on}, 加载敏感词总数={len(self.sensitive_words_dict)}, 正则模式数={len(self.compiled_patterns)}.")
        super().__init__(**kwargs)
    
    def _is_regex_pattern(self, word: str) -> bool:
        """判断字符串是否可能是正则表达式模式"""
        # 简单判断是否包含正则特殊字符
        if any(c in self.REGEX_SPECIAL_CHARS for c in word):
            return True
        # 检查是否有转义序列
        if '\\' in word:
            return True
        # 检查是否有量词
        if re.search(r'\{\d+,?\d*\}', word):
            return True
        return False
    
    def _validate_regex(self, pattern: str) -> bool:
        """验证字符串是否为有效的正则表达式"""
        try:
            re.compile(pattern)
            return True
        except re.error:
            return False
    
    def _load_sensitive_words(self, file_path: Optional[str], encoding: str):
        """从文件加载敏感词列表"""
        if not file_path:
            logger.debug("未提供敏感词文件路径 (sensitive_words_file)，跳过加载")
            return

        try:
            word_count = 0
            with open(file_path, 'r', encoding=encoding) as f:
                for line in f:
                    word = line.strip()
                    if word:
                        # 检查是否是正则表达式格式
                        is_regex = False
                        if self.auto_detect_regex and self._is_regex_pattern(word):
                            if self._validate_regex(word):
                                is_regex = True
                                logger.debug(f"从文件 '{file_path}' 检测到正则表达式格式敏感词: {word}")
                        
                        self.sensitive_words_dict[word] = ("0", is_regex)  # 默认类型为0
                        word_count += 1
            logger.info(f"从文本文件 '{file_path}' 加载了 {word_count} 个敏感词 (默认类型'0')")
        except FileNotFoundError:
            logger.error(f"找不到敏感词文件: '{file_path}'")
        except (IOError, UnicodeDecodeError) as e:
            logger.error(f"读取敏感词文件 '{file_path}' 时发生错误: {e}")
    
    def _load_csv_sensitive_words(self, csv_path: Optional[str], encoding: str):
        """从CSV文件加载敏感词和类型"""
        if not csv_path:
            logger.debug("未提供CSV敏感词文件路径 (sensitive_words_csv)，跳过加载")
            return
        
        try:
            word_count = 0
            regex_count = 0
            skipped_type_zero_count = 0 # 记录因为类型为"0"而被跳过的词数
            with open(csv_path, 'r', encoding=encoding) as f:
                # 尝试自动检测CSV格式
                sample = f.read(1024)
                f.seek(0)
                
                # 检查分隔符
                try:
                    dialect = csv.Sniffer().sniff(sample)
                    reader = csv.reader(f, dialect)
                    logger.debug(f"CSV文件 '{csv_path}' 检测到分隔符: '{dialect.delimiter}', 引号字符: '{dialect.quotechar}'")
                except csv.Error:
                    logger.warning(f"无法自动检测CSV文件 '{csv_path}' 的方言，将使用默认逗号分隔符。如果解析失败，请确保文件格式正确。")
                    f.seek(0) # 重置文件指针
                    reader = csv.reader(f) # 使用默认逗号分隔
                
                # 跳过标题行
                header = next(reader, None)
                if not header or len(header) < 2:
                    logger.warning(f"CSV文件 '{csv_path}' 的标题行可能不正确或列数不足 (应至少包含'敏感词'和'类型编号')。标题行: {header}")
                else:
                    logger.debug(f"CSV文件 '{csv_path}' 的标题行: {header}")
                
                # 读取敏感词和类型
                for i, row in enumerate(reader, start=1): # 从1开始计数行号 (扣除标题)
                    if len(row) >= 2:
                        word = row[0].strip()
                        type_code = row[1].strip()
                        
                        if not word:
                            logger.debug(f"CSV文件 '{csv_path}' 第 {i+1} 行: 敏感词为空，已跳过。")
                            continue
                            
                        if type_code == "0":
                            skipped_type_zero_count += 1
                            # logger.debug(f"CSV文件 '{csv_path}' 第 {i+1} 行: 敏感词 '{word}' 类型为 '0'，已跳过。")
                            continue # 类型为"0"的词条不加载
                            
                        if word:
                            # 检查是否是正则表达式格式
                            is_regex = False
                            if self.auto_detect_regex and self._is_regex_pattern(word):
                                if self._validate_regex(word):
                                    is_regex = True
                                    regex_count += 1
                                    logger.debug(f"从CSV '{csv_path}' 检测到正则表达式格式敏感词: {word} (类型: {type_code})")
                                else:
                                    logger.warning(f"CSV文件 '{csv_path}' 中的敏感词 '{word}' (类型: {type_code}) 疑似正则表达式但无效，将作为普通文本处理。")
                            
                            self.sensitive_words_dict[word] = (type_code, is_regex)
                            word_count += 1
                            
                            # 每1000个词记录一次进度
                            if word_count % 1000 == 0:
                                logger.debug(f"已从CSV '{csv_path}' 加载 {word_count} 个敏感词...")
                    else:
                        logger.warning(f"CSV文件 '{csv_path}' 第 {i+1} 行格式不正确 (列数不足)，已跳过: {row}")
            
            logger.info(f"从CSV文件 '{csv_path}' 加载了 {word_count} 个敏感词 (其中 {regex_count} 个为正则表达式格式)。因类型为'0'而跳过 {skipped_type_zero_count} 个词。")
            
            # 记录敏感词类型分布
            type_counts = {}
            for word, (type_code, _) in self.sensitive_words_dict.items():
                type_counts[type_code] = type_counts.get(type_code, 0) + 1
            
            for type_code, count in sorted(type_counts.items()):
                type_desc = self.SENSITIVE_TYPE_MAP.get(type_code, "未知类型")
                logger.info(f"类型 {type_code} ({type_desc}): {count} 个敏感词")
                
        except FileNotFoundError:
            logger.error(f"找不到CSV敏感词文件: {csv_path}")
        except (IOError, UnicodeDecodeError, csv.Error) as e:
            logger.error(f"读取CSV敏感词文件 {csv_path} 错误: {e}")
    
    def _compile_patterns(self):
        """预编译敏感词的正则表达式。对正则表达式格式和普通文本分别处理。"""
        self.compiled_patterns = []
        if not self.sensitive_words:
            return

        logger.debug(f"开始编译敏感词正则表达式，共 {len(self.sensitive_words)} 个词")
        
        # 分离普通敏感词和正则表达式格式敏感词
        normal_words = []
        regex_patterns = []
        
        for word, (type_code, is_regex) in self.sensitive_words_dict.items():
            if is_regex or self.use_regex:
                regex_patterns.append((word, type_code))
            else:
                normal_words.append((word, type_code))
        
        logger.debug(f"普通敏感词: {len(normal_words)}个，正则表达式格式敏感词: {len(regex_patterns)}个")
        
        # 处理正则表达式格式敏感词
        for pattern, type_code in regex_patterns:
            try:
                if not self.case_sensitive:
                    regex = re.compile(pattern, re.IGNORECASE)
                else:
                    regex = re.compile(pattern)
                # 存储编译后的正则表达式、原始模式字符串和类型
                self.compiled_patterns.append((regex, pattern, type_code))
                logger.debug(f"编译正则表达式敏感词: {pattern}，类型: {type_code}")
            except re.error as e:
                logger.warning(f"跳过无效的正则表达式模式 '{pattern}': {e}")
        
        # 处理普通敏感词，分批次创建合并模式
        if normal_words:
            try:
                # 如果敏感词数量过多，分批处理以避免正则表达式过长
                batch_size = 5000  # 每批次处理的敏感词数量
                word_batches = [normal_words[i:i+batch_size] for i in range(0, len(normal_words), batch_size)]
                
                for batch_idx, words_batch in enumerate(word_batches):
                    # pattern_to_original: maps a lookup key to (original_word, type_code)
                    # patterns_for_regex_str: list of re.escape(original_word)
                    pattern_to_original = {}
                    patterns_for_regex_str = []
                    
                    for word, type_code in words_batch:
                        patterns_for_regex_str.append(re.escape(word))
                        if not self.case_sensitive:
                            # For case-insensitive, map the lowercased word to original
                            # If multiple original words map to the same lowercased word,
                            # the first one encountered takes precedence.
                            lower_word = word.lower()
                            if lower_word not in pattern_to_original:
                                pattern_to_original[lower_word] = (word, type_code)
                        else: # case-sensitive
                            pattern_to_original[word] = (word, type_code)
                    
                    pattern_string = "|".join(patterns_for_regex_str)
                    flags = re.IGNORECASE if not self.case_sensitive else 0
                    combined_regex = re.compile(pattern_string, flags)
                    
                    # 存储编译后的正则表达式、映射和批次索引
                    self.compiled_patterns.append((combined_regex, pattern_to_original, f"batch_{batch_idx}"))
                    logger.debug(f"完成普通敏感词批次 {batch_idx+1}/{len(word_batches)}，包含 {len(words_batch)} 个词，映射表大小: {len(pattern_to_original)}")
                
                logger.info(f"已创建 {len(self.compiled_patterns)} 个正则表达式模式，覆盖所有 {len(self.sensitive_words)} 个敏感词")
            except re.error as e:
                logger.error(f"编译敏感词组合模式时出错: {e}")
            except Exception as e: 
                logger.error(f"编译敏感词组合模式时发生意外错误: {e}")
    
    def _contains_sensitive_word(self, text: str) -> Tuple[bool, Dict[str, str]]:
        """
        快速检查文本中是否包含敏感词，一旦发现任何敏感词立即返回
        
        Returns:
            Tuple[bool, Dict[str, str]]: (是否包含敏感词, 找到的第一个敏感词及其类型)
        """
        if not text or not isinstance(text, str) or not self.compiled_patterns:
            return False, {}
        
        # 遍历所有编译好的模式
        for pattern_info in self.compiled_patterns:
            compiled_regex = pattern_info[0]
            pattern_data = pattern_info[1]  # For regex: original pattern string. For batch: pattern_to_original map.
            type_or_batch_id = pattern_info[2] # For regex: type_code. For batch: batch_id string.
            
            try:
                # 处理正则表达式格式敏感词 (pattern_data is the original regex string)
                if isinstance(pattern_data, str):
                    original_regex_str = pattern_data
                    type_code = type_or_batch_id # This is the type_code for this regex
                    match = compiled_regex.search(text)
                    if match:
                        logger.debug(f"文本中检测到正则表达式敏感词: '{original_regex_str}' (类型: {type_code}), 匹配内容: '{match.group(0)}'")
                        return True, {original_regex_str: type_code}
                
                # 处理普通敏感词批次 (pattern_data is the pattern_to_original map)
                elif isinstance(pattern_data, dict):
                    batch_map = pattern_data
                    match = compiled_regex.search(text)
                    if match:
                        match_text = match.group(0) # Actual text matched by one of the re.escaped words
                        
                        found_word_info = None
                        if not self.case_sensitive:
                            found_word_info = batch_map.get(match_text.lower())
                        else: # case-sensitive
                            found_word_info = batch_map.get(match_text)
                        
                        if found_word_info:
                            original_word, type_code = found_word_info
                            logger.debug(f"文本中检测到普通敏感词: '{original_word}' (类型: {type_code}), 来源于批次 '{type_or_batch_id}', 匹配内容: '{match_text}'")
                            return True, {original_word: type_code}
                        else:
                            # This case should ideally not be reached if the map is built correctly
                            # and match_text is one of the words from the batch.
                            # Could log a warning if this fallback is ever needed or if found_word_info is None.
                            logger.warning(f"组合正则匹配到 '{match_text}' 但无法从批次 '{type_or_batch_id}' 的映射表中找到对应原始词。请检查大小写敏感设置和映射构建逻辑。")
                            # To maintain safety, consider the match from compiled_regex as a generic hit for this batch if specific word cannot be retrieved.
                            # However, this would mean we can't return the specific word and type.
                            # For now, if direct map lookup fails, we assume it's a non-match for a *specific* word,
                            # although the combined regex did fire. This might need refinement if problematic.
                            # The previous version had a loop, which was more robust but complex.
                            # The goal here is simplification. If this 'else' path is hit, it's an issue to investigate.


            except Exception as e:
                logger.warning(f"快速正则表达式查找过程中出错: {e}")
        
        return False, {}
    
    def _format_error_message(self, found_words: Dict[str, str]) -> str:
        """格式化错误信息，包含敏感词及其类型"""
        if not found_words:
            logger.warning("调用 _format_error_message 时 found_words 为空，将返回通用错误消息。")
            return "输出内容包含敏感词"
        
        details = []
        for word, type_code in found_words.items():
            type_desc = self.SENSITIVE_TYPE_MAP.get(type_code, "未知类型")
            details.append(f"'{word}'[类型:{type_code}-{type_desc}]")
        
        return f"输出内容包含敏感词: {', '.join(details)}"
    
    async def async_pre_call_hook(
        self,
        user_api_key_dict: UserAPIKeyAuth,
        cache: DualCache,
        data: dict,
        call_type: Literal[
            "completion",
            "text_completion",
            "embeddings",
            "image_generation",
            "moderation",
            "audio_transcription",
            "pass_through_endpoint",
            "rerank"
        ],
    ) -> Optional[Union[Exception, str, dict]]:
        """
        在LLM API调用前运行，检查并处理输入中的敏感词
        """
        # logger.debug(f"敏感词围栏 [{self.guardrail_name}]: 开始前置调用检查 (pre_call_hook) (类型: {call_type}) - 已禁用敏感词检测")
        
        # # 处理聊天消息中的敏感词
        # _messages = data.get("messages")
        # if _messages:
        #     for i, message in enumerate(_messages):
        #         _content = message.get("content")
        #         if isinstance(_content, str):
        #             # 使用快速检测方法
        #             has_sensitive, found_words = self._contains_sensitive_word(_content)
                    
        #             if has_sensitive:
        #                 logger.info(
        #                     f"敏感词围栏 [{self.guardrail_name}]: 在输入消息 {i+1} 中发现敏感词: {list(found_words.keys())}"
        #                 )
                        
        #                 # 只执行block操作
        #                 error_message = self._format_error_message(found_words)
        #                 logger.warning(f"敏感词围栏 [{self.guardrail_name}]: 拦截用户输入 (消息 {i+1})，原因: {error_message}")
        #                 raise ValueError(f"输入包含敏感词: {error_message}")
        
        # # 处理文本补全中的敏感词
        # _prompt = data.get("prompt")
        # if isinstance(_prompt, str):
        #     # 使用快速检测方法
        #     has_sensitive, found_words = self._contains_sensitive_word(_prompt)
            
        #     if has_sensitive:
        #         logger.info(
        #             f"敏感词围栏 [{self.guardrail_name}]: 在输入prompt中发现敏感词: {list(found_words.keys())}"
        #         )
                
        #         # 只执行block操作
        #         error_message = self._format_error_message(found_words)
        #         logger.warning(f"敏感词围栏 [{self.guardrail_name}]: 拦截用户输入 (prompt)，原因: {error_message}")
        #         raise ValueError(f"Prompt包含敏感词: {error_message}")
        
        # logger.debug(f"敏感词围栏 [{self.guardrail_name}]: 前置调用检查通过 (敏感词检测已禁用)。")
        return data
    
    async def async_moderation_hook(
        self,
        data: dict,
        user_api_key_dict: UserAPIKeyAuth,
        call_type: Literal["completion", "embeddings", "image_generation", "moderation", "audio_transcription"],
    ):
        """
        并行于LLM API调用运行，检查输入是否包含敏感词
        """
        # logger.debug(f"敏感词围栏 [{self.guardrail_name}]: 开始并行审查 (moderation_hook) (类型: {call_type}) - 已禁用敏感词检测")
        
        # # 使用快速检测方法
        # _messages = data.get("messages")
        # if _messages:
        #     for i, message in enumerate(_messages):
        #         _content = message.get("content")
        #         if isinstance(_content, str):
        #             has_sensitive, found_words = self._contains_sensitive_word(_content)
        #             if has_sensitive:
        #                 logger.warning(f"敏感词围栏 [{self.guardrail_name}] (Moderation): 在输入消息 {i+1} 中发现敏感词: {list(found_words.keys())}")
        #                 error_message = self._format_error_message(found_words)
        #                 raise ValueError(f"审查失败 (Moderation): {error_message}")
        
        # _prompt = data.get("prompt")
        # if isinstance(_prompt, str):
        #     has_sensitive, found_words = self._contains_sensitive_word(_prompt)
        #     if has_sensitive:
        #         logger.warning(f"敏感词围栏 [{self.guardrail_name}] (Moderation): 在输入prompt中发现敏感词: {list(found_words.keys())}")
        #         error_message = self._format_error_message(found_words)
        #         raise ValueError(f"审查失败 (Moderation): {error_message}")
        
        # logger.debug(f"敏感词围栏 [{self.guardrail_name}]: 并行审查通过 (敏感词检测已禁用)。")
        pass
    
    async def async_post_call_success_hook(
        self,
        data: dict,
        user_api_key_dict: UserAPIKeyAuth,
        response,
    ):
        """
        在LLM API调用成功后运行，检查输出是否包含敏感词
        """
        logger.debug(f"敏感词围栏 [{self.guardrail_name}]: 开始后置调用成功检查 (post_call_success_hook) - 非流式输出")
        
        if isinstance(response, litellm.ModelResponse):
            for i, choice in enumerate(response.choices):
                if hasattr(choice, "message") and hasattr(choice.message, "content") and choice.message.content:
                    content = choice.message.content
                    # 使用快速检测方法
                    has_sensitive, found_words = self._contains_sensitive_word(content)
                    
                    if has_sensitive:
                        logger.warning(
                            f"敏感词围栏 [{self.guardrail_name}]: 在非流式输出选项 {i+1} 中发现敏感词: {list(found_words.keys())}"
                        )
                        
                        # 只执行block操作
                        error_message = self._format_error_message(found_words)
                        logger.warning(f"敏感词围栏 [{self.guardrail_name}]: 拦截模型非流式输出，原因: {error_message}")
                        raise ValueError(error_message) # 使用格式化后的消息抛出异常
            logger.debug(f"敏感词围栏 [{self.guardrail_name}]: 非流式输出检查通过。")
        else:
            logger.debug(f"敏感词围栏 [{self.guardrail_name}]: 跳过非ModelResponse类型的响应检查: {type(response).__name__}")
    
    async def async_post_call_streaming_iterator_hook(
        self,
        user_api_key_dict: UserAPIKeyAuth,
        response: Any,
        request_data: dict,
    ) -> AsyncGenerator[ModelResponseStream, None]:
        """
        处理流式输出，使用延迟处理机制检测敏感词
        
        实现参考了PII围栏的流式处理逻辑，使用chunk延迟队列机制
        确保能够检测到跨chunk边界的敏感词模式
        """
        logger.debug(f"敏感词围栏 [{self.guardrail_name}]: 开始流式输出检查 (post_call_streaming_iterator_hook)")
        buffer = ""  # 累积接收到的内容，用于敏感词检测
        # 队列存储元组: (原始chunk对象, 该chunk的原始delta内容)
        chunks_queue: List[Tuple[Any, str]] = []
        queue_size = 0  # 追踪队列中的chunk数量
        chunk_counter = 0 # 追踪处理的chunk序号
        
        try:
            async for chunk in response:
                chunk_counter += 1
                # 提取当前chunk的delta内容
                delta_content = ""
                is_last_chunk = False
                
                if hasattr(chunk, "choices") and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    if hasattr(choice, "delta") and hasattr(choice.delta, "content"):
                        if choice.delta.content is not None:
                            delta_content = choice.delta.content
                            logger.debug(f"敏感词围栏 [{self.guardrail_name}] 流式检查 (Chunk {chunk_counter}): 收到新chunk，delta内容: '{delta_content}'")
                    
                    # 检查是否是最后一个chunk
                    if hasattr(choice, "finish_reason") and choice.finish_reason is not None:
                        is_last_chunk = True
                        logger.debug(f"敏感词围栏 [{self.guardrail_name}] 流式检查 (Chunk {chunk_counter}): 检测到最后一个chunk (finish_reason: {choice.finish_reason})")
                
                # 将新内容添加到缓冲区并保存chunk到队列
                buffer += delta_content
                chunks_queue.append((chunk, delta_content))
                queue_size += 1
                logger.debug(f"敏感词围栏 [{self.guardrail_name}] 流式检查 (Chunk {chunk_counter}): 当前buffer长度: {len(buffer)}, 队列长度: {queue_size}")
                
                # 当队列达到延迟处理阈值或收到最后一个chunk时进行处理
                if queue_size > self.delay_chunks or is_last_chunk:
                    logger.debug(f"敏感词围栏 [{self.guardrail_name}] 流式检查 (Chunk {chunk_counter}): 触发处理条件 - 队列长度={queue_size}, 延迟阈值={self.delay_chunks}, 最后chunk={is_last_chunk}")
                    
                    # 使用快速检测方法
                    has_sensitive, found_words = self._contains_sensitive_word(buffer)
                    if has_sensitive:
                        logger.warning(f"敏感词围栏 [{self.guardrail_name}] 流式检查: 在流式输出 (buffer截止至Chunk {chunk_counter}) 中检测到敏感词: {list(found_words.keys())}")
                        # 发现敏感词时，抛出异常终止流
                        error_message = self._format_error_message(found_words)
                        logger.warning(f"敏感词围栏 [{self.guardrail_name}]: 拦截流式输出，原因: {error_message}")
                        raise ValueError(error_message)
                    
                    # 如果没有问题，只释放一个chunk（队列头部）
                    if chunks_queue and not is_last_chunk: # 不在最后一个chunk时才从队列中取出并发送，因为最后一个chunk在循环外处理
                        first_chunk, _ = chunks_queue.pop(0)
                        queue_size -= 1
                        yield first_chunk
                        logger.debug(f"敏感词围栏 [{self.guardrail_name}] 流式检查 (Chunk {chunk_counter}): 已释放一个chunk，剩余队列长度: {queue_size}")
            
            # 流正常结束后检查一次完整的buffer (主要针对最后一个chunk累积的内容)
            logger.debug(f"敏感词围栏 [{self.guardrail_name}] 流式检查: 流处理正常结束 (共 {chunk_counter} chunks)，对完整buffer (长度 {len(buffer)}) 进行最终检查，剩余队列长度: {queue_size}")
            final_has_sensitive, final_found_words = self._contains_sensitive_word(buffer)
            if final_has_sensitive:
                logger.warning(f"敏感词围栏 [{self.guardrail_name}] 流式检查: 在最终检查 (完整buffer) 中发现敏感词: {list(final_found_words.keys())}")
                error_message = self._format_error_message(final_found_words)
                logger.warning(f"敏感词围栏 [{self.guardrail_name}]: 拦截最终流式输出，原因: {error_message}")
                raise ValueError(error_message)
            
            # 释放队列中剩余的所有chunks (通常是最后一个chunk，或者当delay_chunks较大时积累的)
            logger.debug(f"敏感词围栏 [{self.guardrail_name}] 流式检查: 最终检查通过，释放队列中剩余 {queue_size} 个chunks")
            for chunk_to_yield, _ in chunks_queue:
                yield chunk_to_yield
            chunks_queue.clear()
            queue_size = 0
            logger.debug(f"敏感词围栏 [{self.guardrail_name}] 流式检查: 所有剩余chunks已释放，队列清空。")
                
        except ValueError as e:
            # 对于包含敏感词的错误，直接向上传播
            logger.warning(f"敏感词围栏 [{self.guardrail_name}] 流式检查: 引发敏感词错误并向上传播: {str(e)}")
            raise
            
        except Exception as e:
            # 捕获并记录其他意外错误，但仍然向上传播
            logger.error(f"敏感词围栏 [{self.guardrail_name}] 流式检查: 发生意外错误: {type(e).__name__}: {str(e)}", exc_info=True)
            raise 