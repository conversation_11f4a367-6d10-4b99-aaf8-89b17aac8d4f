# -*- encoding: utf-8 -*-
# @ModuleName: sensitive_words_extender_llm.py
# @Author: Gemini
# @Time: 2024/07/05
# @Description: This script uses an LLM to generate and extend new sensitive words based on existing examples.

import pandas as pd
import json
import asyncio
import aiohttp
from tqdm import tqdm
from pathlib import Path

# --- Generation Prompt ---
GENERATION_PROMPT = """
你是一位资深的内容安全专家和语言学家，精通模式分析、风险评估和网络黑话。你的任务是基于一个现有的敏感词及其所属类别，进行联想和扩展，生成5个全新的、但属于同一风险类型的敏感词或正则表达式。

**核心要求**:
1.  **创新性**: 生成的词条必须是全新的，而不是对源词条进行简单的同义词替换或顺序调换。鼓励使用谐音、拆字、影射、变体写法等方式。
2.  **相关性**: 所有生成的新词条必须与给定的“敏感类型”高度相关。
3.  **多样性**: 尽可能提供不同形式的扩展，可以是具体词汇，也可以是更宽泛的正则表达式。
4.  **恶意性**: 生成的内容应明确具有恶意、违规或敏感的意图。一个本身中性的词（如“客服”）不应被错误扩展。
5.  **严格遵守**: 必须不多不少，恰好生成5个新词条。

**输出格式要求**:
你的回答必须是一个JSON对象，其中包含一个名为 `words` 的数组，该数组包含5个字符串。你只需要返回一个JSON对象，不要包含任何额外的解释或注释。也不要包括```json```等标签。

---
**示例 1**
输入模式: `办理证件`
指定的敏感类型: `涉伪证假证`
输出:
{
  "words": ["办正", "办-证", "證件代办", "办\s*各类\s*證", "刻\s*章"]
}
---

**示例 2**
输入模式: `河南人偷井盖`
指定的敏感类型: `地域歧视内容`
输出:
{
  "words": ["荷兰人偷井盖", "河南人偷jingai", "henan人偷井盖", "武汉人搞诈骗", "武汉小缅北"]
}
---

现在，请处理以下输入：
输入模式: `{sensitive_word}`
指定的敏感类型: `{sensitive_type}`
输出:
"""

async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data, timeout=120) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception:
        answer = ""
    return answer

async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.24.45.213:50003/v1/chat/completions"
        messages = [{"role": "user", "content": point[question_name]}]
        body = {
            "model": "secllm-v3",
            "stream": False,
            "top_p": 0.8,
            "temperature": 0.6,
            "repetition_penalty": 1.05,
            "max_tokens": 4096,
            "messages": messages
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer.strip().startswith("{") and answer.strip().endswith("}"):
                break
            await asyncio.sleep(1)

        point[answer_name] = answer
        progress_bar.update(1)
        return point

async def run_llm_batch(data_list, question_name="prompt", answer_name="answer", desc="Running LLM Batch"):
    concurrency_limit = 100
    progress_bar = tqdm(total=len(data_list), desc=desc)
    connector = aiohttp.TCPConnector(limit_per_host=concurrency_limit)
    async with aiohttp.ClientSession(connector=connector) as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar, question_name=question_name, answer_name=answer_name) for data in data_list]
        results = await asyncio.gather(*tasks)
    progress_bar.close()
    return results

def parse_llm_json(response_str, context_word):
    """Safely parses JSON from LLM response."""
    if not isinstance(response_str, str):
        return None
    
    response_str = response_str.strip()
    if not response_str:
        return None

    try:
        if response_str.startswith("```json"):
            response_str = response_str.split("```json")[1].split("```")[0].strip()
        return json.loads(response_str)
    except (json.JSONDecodeError, AttributeError):
        # print(f"\n警告: 解析LLM JSON响应失败。上下文: '{context_word}'")
        return None

def run_generation_process():
    """
    Main pipeline to generate and extend new sensitive words based on existing examples.
    """
    script_dir = Path(__file__).parent
    input_csv_path = script_dir / 'sensitive_words.csv'
    output_csv_path = script_dir / 'sensitive_words_extended.csv'

    if not input_csv_path.exists():
        print(f"错误：输入文件不存在于 '{input_csv_path}'")
        return

    print(f"正在从 '{input_csv_path}' 读取敏感词...")
    df = pd.read_csv(input_csv_path)
    original_count = len(df)
    print(f"原始敏感词数量: {original_count}")

    # --- Prepare and Execute All-in-One LLM Tasks ---
    tasks = df.to_dict('records')
    for task in tasks:
        task['prompt'] = GENERATION_PROMPT.replace("{sensitive_word}", task['敏感词']).replace("{sensitive_type}", task['敏感类型'])

    loop = asyncio.get_event_loop()
    results = loop.run_until_complete(
        run_llm_batch(tasks, answer_name="llm_response", desc="正在生成新的敏感词...")
    )

    # --- Process Results and Save ---
    final_data = []
    print("\n正在处理LLM返回结果并生成最终文件...")

    for result in tqdm(results, desc="处理并保存新词条"):
        final_data.append({
            '敏感词': result['敏感词'],
            '敏感类型': result['敏感类型']
        })
        llm_response_str = result.get("llm_response")
        parsed_data = parse_llm_json(llm_response_str, result['敏感词'])

        if (isinstance(parsed_data, dict) and
            isinstance(parsed_data.get("words"), list)):
            
            for item in parsed_data["words"]:
                if (isinstance(item, str) and item.strip()):
                    final_data.append({
                        '敏感词': item.strip(),
                        '敏感类型': result['敏感类型']
                    })

    # Save validated data
    validated_df = pd.DataFrame(final_data).drop_duplicates()
    validated_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')

    new_count = len(validated_df)
    print("\n处理完成！")
    print(f"最终生成的新敏感词已保存到: '{output_csv_path}'")
    print(f"\n处理统计：")
    print(f"  - 原始词条数: {original_count}")
    print(f"  - 生成新词条数: {new_count} (目标: {original_count * 5})")

if __name__ == '__main__':
    run_generation_process() 