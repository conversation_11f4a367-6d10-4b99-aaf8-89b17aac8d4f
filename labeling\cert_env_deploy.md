CERT环境部署

一、拉起容器

```
# 这个是旧的镜像，尽量使用T18以上版本的镜像，支持Qwen3

podman run --name mindie_secllm -it --net=host --shm-size=250g --privileged=true \
    -w /home \
    --pids-limit=65536 \
    --device=/dev/davinci_manager \
    --device=/dev/hisi_hdc \
    --device=/dev/devmm_svm \
    --env MINDIE_LOG_TO_STDOUT=1 \
    --env MINDIE_LOG_TO_FILE=0 \
    -v /usr/local/Ascend/driver:/usr/local/Ascend/driver:ro \
    -v /usr/local/sbin:/usr/local/sbin:ro \
    -v /usr/local/dcmi:/usr/local/dcmi \
    -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
    -v /tmp:/tmp \
    -v /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime \
    -v /home:/home \
    -v /home:/home \
    swr.cn-south-1.myhuaweicloud.com/ascendhub/mindie:2.0.RC2-800I-A2-py311-openeuler24.03-lts /bin/bash

```

二、容器环境配置

可能导致容器无法运行，本步骤跳过！

```
# 进入容器
podman exec -it mindie_secllm bash

# 安装miniconda
cd /home/<USER>/
bash Miniconda3-py311_25.3.1-1-Linux-aarch64.sh

# 在 ./~bashrc 中添加以下环境变量
source /usr/local/Ascend/ascend-toolkit/set_env.sh
source /usr/local/Ascend/mindie/set_env.sh
source /usr/local/Ascend/nnal/atb/set_env.sh
export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver:/usr/lib64:/lib:/lib:/usr/local/Ascend/atb-models/lib:/usr/local/Ascend/mindie/latest/mindie-llm/lib:/usr/local/Ascend/mindie/latest/mindie-llm/lib/grpc:/usr/local/Ascend/mindie/latest/mindie-service/lib:/usr/local/Ascend/mindie/latest/mindie-service/lib/grpc:/usr/local/Ascend/mindie/latest/mindie-torch/lib:/usr/local/Ascend/mindie/latest/mindie-rt/lib:/usr/local/Ascend/nnal/atb/latest/atb/cxx_abi_0/lib:/usr/local/Ascend/nnal/atb/latest/atb/cxx_abi_0/examples:/usr/local/Ascend/nnal/atb/latest/atb/cxx_abi_0/tests/atbopstest:/usr/local/Ascend/ascend-toolkit/latest/tools/aml/lib64:/usr/local/Ascend/ascend-toolkit/latest/tools/aml/lib64/plugin:/usr/local/Ascend/ascend-toolkit/latest/lib64/plugin/nnengine:/usr/local/Ascend/ascend-toolkit/latest/opp/built-in/op_impl/ai_core/tbe/op_tiling/lib/linux/aarch64:/usr/local/Ascend/ascend-toolkit/latest/lib64:/usr/local/Ascend/ascend-toolkit/latest/lib64/plugin/opskernel:/usr/local/Ascend/driver/lib64/driver:/usr/local/Ascend/driver/lib64/common:
export ATB_SPEED_HOME_PATH=/usr/local/Ascend/atb-models
export PYTHONPATH=/usr/local/lib/python3.11/site-packages:/usr/local/lib64/python3.11/site-packages:/usr/local/Ascend/atb-models:/usr/local/Ascend/mindie/latest/mindie-llm:/usr/local/Ascend/mindie/latest/mindie-llm/lib:/usr/local/Ascend/mindie/latest/mindie-service/bin:/usr/local/lib/python3.11/site-packages:/usr/local/lib64/python3.11/site-packages:/usr/local/Ascend/ascend-toolkit/latest/python/site-packages:/usr/local/Ascend/ascend-toolkit/latest/opp/built-in/op_impl/ai_core/tbe:
export LANG="zh_CN.UTF-8"
export LC_ALL="zh_CN.UTF-8"

# 更新环境变量，或者重新进入容器
source ~/.bashrc

# 安装atb包
cd $ATB_SPEED_HOME_PATH
python -m pip install atb_llm-0.0.1-py3-none-any.whl
/usr/bin/python -m pip install atb_llm-0.0.1-py3-none-any.whl

# 安装transformers依赖
cd /home/<USER>/transformers_offline_packages
python -m pip install --no-index --find-links=. --force-reinstall transformers
/usr/bin/python -m pip install --no-index --find-links=. --force-reinstall transformers
```


三、容器中文环境配置

若容器已经支持中文，可以跳过。

```
# 安装中文依赖包
cd /home/<USER>/font-locale
rpm -Uvh glibc-all-langpacks-2.38-54.oe2403.aarch64.rpm
rpm -Uvh glibc-locale-archive-2.38-54.oe2403.aarch64.rpm
rpm -Uvh fonts-filesystem-4.0.2-2.oe2403.noarch.rpm
rpm -Uvh freetype-2.13.2-1.oe2403.aarch64.rpm
rpm -Uvh dejavu-fonts-2.37-2.oe2403.noarch.rpm
rpm -Uvh fontconfig-2.15.0-1.oe2403.aarch64.rpm
rpm -Uvh wqy-microhei-fonts-0.2.0-0.25.oe2403.noarch.rpm
cp locale.conf /etc/
fc-cache -fv
export LANG="zh_CN.UTF-8"
export LC_ALL="zh_CN.UTF-8"


# 将赛题文件转为utf-8编码，此时可以正常浏览utf-8编码文件，不会乱吗
iconv -f GBK -t UTF-8 S5_sample_date.csv > S5_sample_date_utf8.csv

```


四、修改推理服务配置文件

修改/usr/local/Ascend/mindie/latest/mindie-service/conf/config.json
也可使用路径：$MIES_INSTALL_PATH/conf/config.json

主要涉及到以下修改：

```
"httpsEnabled" : false,

"npuDeviceIds" : [[0,1,2,3]],  # 改为对应的卡的数量，和npu-smi info显示的device id不对应

"maxSeqLen" : 32768,
"maxInputTokenLen" : 32768,

"modelName" : "qwen3",  # 自行修改，使用时API接口中和该值匹配即可
"modelWeightPath" : "/data/models/Qwen3-32B",  # 修改为模型路径

"worldSize" : 4,  # 和 npuDeviceIds 匹配
"cpuMemSize" : 15,
"npuMemSize" : 5,

"maxPrefillBatchSize" : 50,
"maxPrefillTokens" : 327680,

"maxBatchSize" : 100,
"maxIterTimes" : 16384,  # 模型的最大生成长度

```

如果遇到显存不足报错，对应的减小 `npuMemSize`, `maxPrefillBatchSize`, `maxPrefillTokens`, `maxBatchSize` 等参数即可。



五、启动、测试Server服务


```
# 设置环境变量，每次重新进入容器都需要设置！
source /usr/local/Ascend/ascend-toolkit/set_env.sh
source /usr/local/Ascend/nnal/atb/set_env.sh
source /usr/local/Ascend/mindie/set_env.sh
source /usr/local/Ascend/atb-models/set_env.sh


# 测试直接推理，修改为正确的模型路径
# 这一步不会有输出，没报错即代表可以正常推理
cd $ATB_SPEED_HOME_PATH
python examples/run_pa.py --model_path /path-to-weights 

# 后台启动服务
cd $MIES_INSTALL_PATH
nohup ./bin/mindieservice_daemon > output.log 2>&1 &


# 测试推理服务

# 这里的模型名称改为配置文件中的 `modelName` 的值即可。

curl --location 'http://127.0.0.1:1025/v1/chat/completions' \
--header 'Content-Type: application/json' \
--data-raw '{
    "model": "qwen3",
    "messages": [
        {
            "role": "user",
            "content": "你是谁？\n"
        }
    ],
    "temperature": 0.5,
    "top_p": 0.95,
    "repetition_penalty": 1.05,
    "max_tokens": 4000,
    "stream": false
}'

```

