import pandas as pd
import os
from collections import Counter, defaultdict
from sklearn.metrics import classification_report
import numpy as np


base_file = "/home/<USER>/risk_analysis/data/S5_stage_one_data.csv"
# model_files = [
#     {
#         "model_name": "Qwen3-235B",
#         "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B_2025062209_answer.csv"
#     },
#     {
#         "model_name": "Qwen3-235B-2",
#         "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B_2025062218_answer.csv"
#     },
#     {
#         "model_name": "Qwen3-235B-3",
#         "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B_2025062312_answer.csv"
#     },
#     {
#         "model_name": "secllm-v3",
#         "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_secllm-v3_2025061817_answer.csv"
#     },
#     {
#         "model_name": "secllm-v3-2",
#         "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_secllm-v3_2025062218_answer.csv"
#     },
#     {
#         "model_name": "secllm-v3-3",
#         "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_secllm-v3_2025062312_answer.csv"
#     },
#     {
#         "model_name": "Qwen3-32B",
#         "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-32B_2025062218_answer.csv"
#     },
#     {
#         "model_name": "Qwen3-32B-2",
#         "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-32B_2025062312_answer.csv"
#     },
#     {
#         "model_name": "DeepSeek-R1-0528-Qwen3-8B",
#         "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_DeepSeek-R1-0528-Qwen3-8B_2025062412_answer.csv"
#     },
#     {
#         "model_name": "DeepSeek-R1-Distill-Qwen-32B",
#         "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_DeepSeek-R1-Distill-Qwen-32B_2025062412_answer.csv"
#     }
# ]

# model_groups_config = [
#     {
#         "group_name": "Qwen3-235B",
#         "models": [
#             {
#                 "model_name": "Qwen3-235B",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B_2025062209_answer.csv"
#             },
#             {
#                 "model_name": "Qwen3-235B-2",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B_2025062218_answer.csv"
#             },
#             {
#                 "model_name": "Qwen3-235B-3",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B_2025062312_answer.csv"
#             }
#         ]
#     },
#     {
#         "group_name": "secllm-v3",
#         "models": [
#             {
#                 "model_name": "secllm-v3",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_secllm-v3_2025061817_answer.csv"
#             },
#             {
#                 "model_name": "secllm-v3-2",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_secllm-v3_2025062218_answer.csv"
#             },
#             {
#                 "model_name": "secllm-v3-3",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_secllm-v3_2025062312_answer.csv"
#             }
#         ]
#     },
#     {
#         "group_name": "other",
#         "models": [
#             {
#                 "model_name": "Qwen3-32B",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-32B_2025062218_answer.csv"
#             },
#             {
#                 "model_name": "Qwen3-32B-2",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-32B_2025062312_answer.csv"
#             },
#             {
#                 "model_name": "DeepSeek-R1-0528-Qwen3-8B",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_DeepSeek-R1-0528-Qwen3-8B_2025062412_answer.csv"
#             },
#             # {
#             #     "model_name": "DeepSeek-R1-Distill-Qwen-32B",
#             #     "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_DeepSeek-R1-Distill-Qwen-32B_2025062412_answer.csv"
#             # }
#         ]
#     }
# ]

# Manually define model groups
# model_groups_config = [
#     {
#         "group_name": "Qwen-235B",
#         "models": [
#             {
#                 "model_name": "Qwen3-235B",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_v2_Qwen3-235B_2025062719_answer.csv"
#             },
#             {
#                 "model_name": "Qwen3-235B-2",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_v2_Qwen3-235B_2025062912_answer.csv"
#             },
#             {
#                 "model_name": "Qwen3-235B-3",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B_2025062209_answer.csv"
#             }
#         ]


#     },
#     {
#         "group_name": "secllm-v3",
#         "models": [
#             {
#                 "model_name": "secllm-v3",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_v2_secllm-v3_2025062719_answer.csv"
#             },
#             {
#                 "model_name": "secllm-v3-2",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_v2_secllm-v3_2025062906_answer.csv"
#             },
#             {
#                 "model_name": "secllm-v3-3",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_v2_secllm-v3_2025062919_answer.csv"
#             },
#             {
#                 "model_name": "secllm-v3-4",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_secllm-v3_2025061817_answer.csv"
#             },
#             {
#                 "model_name": "secllm-v3-5",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_secllm-v3_2025062218_answer.csv"
#             },
#         ]
#     },
#     # {
#     #     "group_name": "athene-v2",
#     #     "models": [
#     #         {
#     #             "model_name": "athene-v2",
#     #             "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_v2_athene-v2_2025062719_answer.csv"
#     #         },
#     #         {
#     #             "model_name": "athene-v2-2",
#     #             "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_v2_athene-v2_2025062906_answer.csv"
#     #         },
#     #         {
#     #             "model_name": "athene-v2-3",
#     #             "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_v2_athene-v2_2025062919_answer.csv"
#     #         }
#     #     ]
#     # },
#     {
#         "group_name": "other",
#         "models": [
#             {
#                 "model_name": "Qwen3-32B",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-32B_2025062218_answer.csv"
#             },
#             {
#                 "model_name": "Qwen3-32B-2",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-32B_2025062312_answer.csv"
#             },
#             {
#                 "model_name": "DeepSeek-R1-0528-Qwen3-8B",
#                 "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_DeepSeek-R1-0528-Qwen3-8B_2025062412_answer.csv"
#             },
#             # {
#             #     "model_name": "DeepSeek-R1-Distill-Qwen-32B",
#             #     "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_DeepSeek-R1-Distill-Qwen-32B_2025062412_answer.csv"
#             # }
#         ]
#     }
# ]


model_groups_config = [
    {
        "group_name": "Qwen-235B-v3",
        "models": [
            {
                "model_name": "Qwen3-235B-v3-thinking-1",
                "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B-thinking_2025072001.csv"
            },
            # {
            #     "model_name": "Qwen3-235B-v3-nothinking-1",
            #     "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B-nothinking_2025072001.csv"
            # }
        ]
    },
    # {
    #     "group_name": "Qwen-235B-v2",
    #     "models": [
    #         {
    #             "model_name": "Qwen3-235B-v2-thinking-1",
    #             "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_v2_Qwen3-235B_2025062719_answer.csv"
    #         },
    #         {
    #             "model_name": "Qwen3-235B-v2-thinking-2",
    #             "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_v2_Qwen3-235B_2025062912_answer.csv"
    #         }
    #     ]


    # },
    # {
    #     "group_name": "Qwen3-235B-v1",
    #     "models": [
    #         {
    #             "model_name": "Qwen3-235B-v1-thinking-1",
    #             "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B_2025062209_answer.csv"
    #         },
    #         {
    #             "model_name": "Qwen3-235B-v1-thinking-2",
    #             "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B_2025062218_answer.csv"
    #         },
    #         {
    #             "model_name": "Qwen3-235B-v1-thinking-3",
    #             "path": "/home/<USER>/risk_analysis/result/S5_stage_one_output_Qwen3-235B_2025062312_answer.csv"
    #         }
    #     ]
    # },
]


# Create a flat list of all models for merging and other operations
model_files = [model for group in model_groups_config for model in group['models']]

# 输出文件路径
output_file = '/home/<USER>/risk_analysis/result/merged/final_analysis_results_group_v3_20250721.csv'

# 手动标注文件路径
manual_labels_file = '/home/<USER>/risk_analysis/data/result_0630_1735.csv'


# 检查是否有配置文件
if not model_files:
    print("请在脚本中配置 'model_files' 列表。")
    exit()

print(f"找到 {len(model_files)} 个待合并的文件:")
for f in model_files:
    print(f"- 模型: {f['model_name']},路径: {f['path']}")

# 1. 使用 base_file 作为基础，只取 问答id 字段
print(f"\n使用基础文件: {base_file}")
try:
    base_df = pd.read_csv(base_file)
    # 为防止列名中存在空格或换行符，进行清理
    base_df.columns = [col.strip().replace('\n', '').replace(' ', '') for col in base_df.columns]
    base_df = base_df[['问答id', '大模型的输入内容', '生成的回答']]
except FileNotFoundError:
    print(f"错误: 基础文件 '{base_file}' 未找到。程序退出。")
    exit()
except KeyError:
    print(f"错误: 基础文件 '{base_file}' 中未找到 '问答id' 列。程序退出。")
    exit()

# 2. 循环合并所有模型文件到 base_df
for model_info in model_files:
    model_name = model_info['model_name']
    path = model_info['path']
    try:
        temp_df = pd.read_csv(path)
        temp_df = temp_df.add_suffix(f"_{model_name}")
        temp_df.rename(columns={f"问答id_{model_name}": '问答id'}, inplace=True)
        base_df = pd.merge(base_df, temp_df, on='问答id', how='left')
    except FileNotFoundError:
        print(f"警告: 模型文件未找到，将跳过: {path}")
        continue


print("\n文件合并完成。开始进行分组投票决策...")

# --- New Grouped Voting Logic ---

# 1. Get groups from manual config
ordered_group_names = [group['group_name'] for group in model_groups_config]
model_groups = {group['group_name']: group['models'] for group in model_groups_config}

print("\n模型分组情况 (手动配置):")
for group_name in ordered_group_names:
    models = model_groups[group_name]
    print(f"- 分组: {group_name}, 模型: {[m['model_name'] for m in models]}")

def get_vote_result(votes, priority_list, row):
    """
    通用投票函数.
    - votes: 投票列表 (['是', '否', ...])
    - priority_list: 用于打破平票的模型信息列表.
    - row: 当前处理的数据行 (pandas Series), 用于按优先级取值.
    """
    if not votes:
        return '否', 0, 0 # 默认'否'

    vote_counts = Counter(votes)
    risky_votes = vote_counts.get('是', 0)
    safe_votes = vote_counts.get('否', 0)

    if risky_votes > safe_votes:
        return '是', risky_votes, safe_votes
    elif risky_votes < safe_votes:
        return '否', risky_votes, safe_votes
    else:  # 平票 (e.g., 1-1, 2-2, or 0-0 if votes somehow empty)
        if risky_votes > 0: # 确实存在有效票数的平局
            # 按优先级列表打破平局
            for model_info in priority_list:
                model_column = f'is_risky_{model_info["model_name"]}'
                if model_column in row and pd.notna(row[model_column]):
                    return row[model_column], risky_votes, safe_votes
            # 如果优先级列表无法打破平局，采纳谨慎策略
            return '是', risky_votes, safe_votes
        else: # 0-0 票, 没有有效投票
            return '否', risky_votes, safe_votes


final_results = []
for index, row in base_df.iterrows():
    # --- 组内投票 ---
    group_level_votes = {}
    # 使用有序的 group name 保证优先级
    for group_name in ordered_group_names:
        models_in_group = model_groups[group_name]
        group_internal_votes = []
        for model_info in models_in_group:
            col = f'is_risky_{model_info["model_name"]}'
            if col in row and pd.notna(row[col]):
                group_internal_votes.append(row[col])
        
        # 组内投票，优先级按 model_files 中顺序
        group_vote_result, _, _ = get_vote_result(group_internal_votes, models_in_group, row)
        group_level_votes[group_name] = group_vote_result

    # --- New Priority-Based Final Decision ---
    priority_based_final_vote = '否' # Default to '否' if no group has a say
    for group_name in ordered_group_names:
        if group_name in group_level_votes and pd.notna(group_level_votes[group_name]):
            priority_based_final_vote = group_level_votes[group_name]
            break # Stop at the first group that has a valid vote

    # --- 最终投票 (原始逻辑) ---
    final_vote_list = list(group_level_votes.values())
    
    # 最终投票的优先级由 ordered_group_names 决定
    final_priority_list = [{'model_name': gn} for gn in ordered_group_names]
    # 最终投票的虚拟row
    final_vote_row = {f"is_risky_{gn}": gv for gn, gv in group_level_votes.items()}

    final_is_risky, final_risky_votes, final_safe_votes = get_vote_result(final_vote_list, final_priority_list, final_vote_row)

    # 检查是否需要人工校验：存在不同意见，或没有任何有效投票
    needs_manual_review = 0
    if (final_risky_votes > 0 and final_safe_votes > 0) or \
       (final_risky_votes == 0 and final_safe_votes == 0):
        needs_manual_review = 1
        
    result_row = {
        'final_is_risky': final_is_risky,
        'final_is_risky_priority': priority_based_final_vote,
        'needs_manual_review': needs_manual_review
    }
    # 添加各组的投票结果，方便追溯
    for group_name in ordered_group_names:
        result_row[f'{group_name}_vote'] = group_level_votes.get(group_name, 'N/A')

    final_results.append(result_row)


results_df = pd.DataFrame(final_results)

# 合并投票结果到主DataFrame
final_df = pd.concat([base_df, results_df], axis=1)

# 调整列顺序，把投票结果列放到前面
group_vote_cols = [f'{gn}_vote' for gn in ordered_group_names]
is_risky_cols = [f"is_risky_{model_info['model_name']}" for model_info in model_files if f"is_risky_{model_info['model_name']}" in final_df.columns]
cols_to_keep = ['问答id', '大模型的输入内容', '生成的回答', 'final_is_risky', 'final_is_risky_priority', 'needs_manual_review'] + group_vote_cols + is_risky_cols

for model_info in model_files:
    raw_text_col = f"raw_response_text_{model_info['model_name']}"
    # 确保列存在（以防某个文件加载失败）
    if raw_text_col in final_df.columns:
        cols_to_keep.append(raw_text_col)

final_df = final_df[cols_to_keep]

# 为 raw_response_text 列添加模型标识
for model_info in model_files:
    model_name = model_info['model_name']
    raw_text_col = f"raw_response_text_{model_name}"
    if raw_text_col in final_df.columns:
        tag = f"<model_name>{model_name}</model_name>\n"
        final_df[raw_text_col] = final_df[raw_text_col].apply(
            lambda x: tag + str(x) if pd.notna(x) else x
        )

# 保存结果
output_dir = os.path.dirname(output_file)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

final_df.to_csv(output_file, index=False, encoding='utf-8-sig')

print(f"\n处理完成！结果已保存至 '{output_file}'")
print(f"总处理行数: {len(final_df)}")
print(f"需要人工审核的行数: {final_df['needs_manual_review'].sum()}") 


# --- Performance Evaluation against Manual Labels ---
if os.path.exists(manual_labels_file):
    print(f"\n--- 开始评估模型准确率 (基于: {manual_labels_file}) ---")
    
    try:
        manual_df = pd.read_csv(manual_labels_file)
        # 清理列名，移除空格和括号中的内容
        manual_df.columns = [col.split(' ')[0].strip() for col in manual_df.columns]
        
        # 验证列名是否存在
        if '问答id' not in manual_df.columns or '是否有风险（0/1:0为无风险，1为有风险）' not in manual_df.columns:
            print(f"错误: 手动标注文件 '{manual_labels_file}' 必须包含 '问答id' 和 '是否有风险（0/1:0为无风险，1为有风险）' 列。")
        else:
            manual_df.rename(columns={'是否有风险（0/1:0为无风险，1为有风险）': 'manual_label'}, inplace=True)
            
            # 合并数据，只保留有手动标注的行
            eval_df = pd.merge(final_df, manual_df[['问答id', 'manual_label']], on='问答id', how='inner')
            
            if eval_df.empty:
                print("错误: 找不到任何与手动标注文件匹配的 '问答id'。")
            else:
                print(f"找到 {len(eval_df)} 条可用于评估的已标注数据。")

                # 定义评估函数 - 使用用户自定义的F1计算方法
                def evaluate_performance_with_custom_f1(y_true, y_pred, model_name):
                    """
                    使用用户自定义的F1计算方法进行评估：
                    - 将有风险标注为1
                    - 准确率 = TP/(TP+FP)
                    - 漏报率 = FN/(TP+FN)
                    - F1 = (2*(1-漏报率)*准确率)/((1-漏报率)+准确率)*100)
                    """
                    
                    # 确保 y_pred 中的所有值都存在于映射中
                    valid_indices = y_pred.isin([0, 1])
                    if not valid_indices.all():
                        print(f"\n- 警告: '{model_name}' 的预测结果中包含无效值，将只评估有效部分。")
                    
                    y_true_filtered = y_true[valid_indices]
                    y_pred_filtered = y_pred[valid_indices]
                    
                    if len(y_true_filtered) == 0:
                        print(f"\n- {model_name}: 无有效预测可供评估。")
                        return
                    
                    # 计算TP, FP, FN, TN
                    TP = ((y_true_filtered == 1) & (y_pred_filtered == 1)).sum()
                    FP = ((y_true_filtered == 0) & (y_pred_filtered == 1)).sum()
                    FN = ((y_true_filtered == 1) & (y_pred_filtered == 0)).sum()
                    TN = ((y_true_filtered == 0) & (y_pred_filtered == 0)).sum()
                    
                    total = TP + FP + FN + TN
                    if total == 0:
                        print(f"\n- {model_name}: 无有效样本进行评估。")
                        return
                    
                    # 计算各项指标
                    accuracy = TP / (TP + FP) if (TP + FP) > 0 else 0  # 准确率 = TP/(TP+FP)
                    miss_rate = FN / (TP + FN) if (TP + FN) > 0 else 0  # 漏报率 = FN/(TP+FN)
                    
                    # 计算用户自定义的F1值
                    if (1 - miss_rate) + accuracy > 0:
                        custom_f1 = (2 * (1 - miss_rate) * accuracy) / ((1 - miss_rate) + accuracy) * 100
                    else:
                        custom_f1 = 0
                    
                    print(f"\n--- 评估: {model_name} ---")
                    print(f"样本总数: {total}")
                    print(f"TP (正确识别风险): {TP}")
                    print(f"FP (误报): {FP}")
                    print(f"FN (漏报): {FN}")
                    print(f"TN (正确识别安全): {TN}")
                    print(f"准确率: {accuracy:.4f}")
                    print(f"漏报率: {miss_rate:.4f}")
                    print(f"用户自定义F1: {custom_f1:.2f}%")
                    
                    # 同时保留原始classification report作为对比
                    print("\n原始classification report:")
                    report = classification_report(y_true_filtered, y_pred_filtered, 
                                                 target_names=['无风险 (0)', '有风险 (1)'], zero_division=0)
                    print(report)

                # 定义风险判断的映射
                risk_mapping = {'是': 1, '否': 0}
                
                # 评估最终投票结果 (原始投票逻辑)
                y_true = eval_df['manual_label']
                final_pred_col = 'final_is_risky'
                if final_pred_col in eval_df.columns:
                    y_pred_final = eval_df[final_pred_col].map(risk_mapping).fillna(-1).astype(int)
                    evaluate_performance_with_custom_f1(y_true, y_pred_final, "最终投票 (多数投票)")

                # 评估基于优先级选择的最终结果 (新逻辑)
                priority_pred_col = 'final_is_risky_priority'
                if priority_pred_col in eval_df.columns:
                    y_pred_priority = eval_df[priority_pred_col].map(risk_mapping).fillna(-1).astype(int)
                    evaluate_performance_with_custom_f1(y_true, y_pred_priority, "最终投票 (基于分组优先级)")
                
                # 评估每个分组的投票结果
                for col in group_vote_cols:
                    if col in eval_df.columns:
                        y_pred_group = eval_df[col].map(risk_mapping).fillna(-1).astype(int)
                        evaluate_performance_with_custom_f1(y_true, y_pred_group, f"分组投票 ({col})")

                # 评估每个独立模型的结果
                for col in is_risky_cols:
                    if col in eval_df.columns:
                        y_pred_model = eval_df[col].map(risk_mapping).fillna(-1).astype(int)
                        evaluate_performance_with_custom_f1(y_true, y_pred_model, f"独立模型 ({col})")

    except Exception as e:
        print(f"处理手动标注文件时发生错误: {e}")
else:
    print(f"\n未找到手动标注文件: {manual_labels_file}，跳过性能评估。")


## 不用人工校验的只按顺序取第一个模型的结果即可

## 过滤出需要人工校验的行，保存全部结果