# -*- encoding: utf-8 -*-
# @ModuleName: sensitive_words_cleaner.py
# @Author: <PERSON><PERSON><PERSON>
# @Time: 2021/11/22 16:07
# @Modified: 2024 - 敏感词清洗脚本


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset
import os
import yaml


# 敏感词类型映射
SENSITIVE_TYPE_MAP = {
    "0": "不做划分",
    "1": "煽动颠覆国家政权、推翻社会主义制度",
    "2": "危害国家安全和利益、损害国家形象",
    "3": "煽动分裂国家、破坏国家统一和社会稳定",
    "4": "宣扬恐怖主义、极端主义",
    "5": "宣扬民族仇恨",
    "6": "宣扬暴力、淫秽色情",
    "7": "传播虚假有害信息",
    "8": "其他法律、行政法规禁止的内容",
    "9": "民族歧视内容",
    "10": "信仰歧视内容",
    "11": "国别歧视内容",
    "12": "地域歧视内容",
    "13": "性别歧视内容",
    "14": "年龄歧视内容",
    "15": "职业歧视内容",
    "16": "健康歧视内容",
    "17": "其他方面歧视内容",
    "18": "侵犯他人其他合法权益",
    "19": "侵害他人个人信息权益",
    "20": "侵害他人名誉权",
    "21": "侵害他人荣誉权",
    "22": "侵害他人肖像权",
    "23": "侵害他人隐私权",
    "24": "危害他人身心健康",
    "25": "利用算法、数据、平台等优势，实施垄断和不正当竞争行为",
    "26": "其他商业违法违规行为",
    "27": "侵犯他人知识产权",
    "28": "违反商业道德",
    "29": "泄露他人商业秘密"
}


async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.37.4.3:50003/v1/chat/completions"


        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "secllm-v3", #"Mistral-Large-Instruct-2407",  #"secllm-v2",  #"Meta-Llama-3.1-405B-Instruct",
            "stream": False,
            "top_p": 0.01,
            "temperature": 0.01,
            "repetition_penalty": 1.05,
            "max_tokens": 1000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list, question_name="prompt", answer_name="answer"):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar, question_name=question_name, answer_name=answer_name) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")
# 读取敏感词CSV文件

def read_sensitive_words_csv(csv_file_path):
    """读取敏感词CSV文件并添加label映射"""
    try:
        df = pd.read_csv(csv_file_path, encoding='utf-8')
        sensitive_words_data = []
        
        for _, row in df.iterrows():
            sensitive_word = row['敏感词']
            label = str(row['label'])
            label_name = SENSITIVE_TYPE_MAP.get(label, "未知类型")
            
            sensitive_words_data.append({
                "sensitive_word": sensitive_word,
                "label": label,
                "label_name": label_name
            })
        
        return sensitive_words_data
    except Exception as e:
        print(f"读取CSV文件出错: {e}")
        return []

csv_file_path = "/home/<USER>/llm_guardrail/TC260分类-敏感词典/sensitive_words_filter.csv"
sensitive_words_data = read_sensitive_words_csv(csv_file_path)

print(f"读取到 {len(sensitive_words_data)} 个敏感词")

sensitive_word_clean_prompt = """
你是一个专业的违规关键词清洗专家。我需要你分析以下违规关键词的合理性和准确性。

该关键词后续会用来过滤违规内容，请务必确保输出的结果不会产生误报，宁愿漏报也绝不误报， 同时保持对真正违规内容的有效识别。

违规关键词信息：
- 违规关键词/正则表达式：{sensitive_word}
- 违规类型：{label_name}

违规类型列表：
{sensitive_type_list}

你评判违规关键词时，需要符合中国的政治、国情、法律、法规、道德、文化、社会、伦理等。违规范围不只是明显的触犯违规内容，还包括可能的诱导。
注意，违规关键词可能使用拼音、英文、数字、符号等组合，且可能使用缩写、谐音、同音字、同音词等，从而躲避监控，需要综合考虑。

请按照以下思路进行分析：
1. 分析该违规关键词是否确实属于违规关键词（违规等）。
2. 分析该违规关键词是否过于宽泛，容易产生误报。
3. 分析该违规关键词是否过于少见或限制过多、过于具体，基本无法触发：需要注意控制违规词的数量，如果违规词难以触发、比较少见，考虑是否需要删除。避免违规词过多导致误报或检测效率降低。
4. 分析该违规关键词是否存在字符、语法上的错误。
5. 如果是正则表达式，检查其语法和逻辑正确性。
6. 考虑对违规关键词/正则表达式进行优化：你可以对违规关键词/正则表达式进行优化，从而提高其准确性，但不要改变希望检测的内容。（也包括将违规关键词修改为正则表达式，或者将正则表达式修改为违规关键词）
8. 优化思路：如果需要优化违规关键词/正则表达式，请先给出优化的思路，如果优化后的是正则表达式，请分析触发的情况，给出正则表达式的语法和逻辑正确性，绝对要控制违规关键词不要误报，同时要考虑触发的概率。最后再给出优化后的违规关键词/正则表达式。

处理要求：
- 如果 违规关键词/正则表达式 合理且准确，原样输出该 违规关键词/正则表达式
- 如果 违规关键词/正则表达式 需要优化（过于宽泛、容易误报、语法错误等），输出优化后的 违规关键词/正则表达式
- 如果 违规关键词/正则表达式 应该删除（不属于违规关键词、过于宽泛、基本无法触发、严重错误等），输出"空"

请务必确保输出的结果不会产生误报，宁愿漏报也绝不误报， 同时保持对真正违规内容的有效识别。

请使用xml格式给出你的分析过程，然后给出处理后的违规关键词/正则表达式。

格式如下：
<analysis>
    <reasoning>
        [你的分析过程]
    </reasoning>
    <sensitive_word>
        [处理后的违规关键词/正则表达式]
    </sensitive_word>
    <type>
        [违规类型]
    </type>
</analysis>

"""

# 为每个敏感词生成清洗prompt
for item in sensitive_words_data:
    prompt = sensitive_word_clean_prompt.format(
        sensitive_word=item["sensitive_word"],
        label_name=item["label_name"],
        label=item["label"],
        sensitive_type_list="\n".join([f" - {v}" for _, v in SENSITIVE_TYPE_MAP.items()])
    )
    item["prompt"] = prompt

print("\n\ndata size: %s" % len(sensitive_words_data))
print("\ndata example: \n\n%s" % sensitive_words_data[0])

print("\n\nstart 敏感词清洗...\n\n")

# 执行敏感词清洗
loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(sensitive_words_data, question_name="prompt", answer_name="answer"))

print("敏感词清洗完成")

def parse_xml_response(xml_response):
    """
    解析XML格式的LLM响应，提取敏感词和推理内容
    
    Args:
        xml_response: XML格式的响应字符串
    
    Returns:
        tuple: (cleaned_word, reasoning)
    """
    try:
        # 提取 <sensitive_word> 标签内容
        sensitive_word_start = xml_response.find('<sensitive_word>')
        sensitive_word_end = xml_response.find('</sensitive_word>')
        
        if sensitive_word_start != -1 and sensitive_word_end != -1:
            sensitive_word_start += len('<sensitive_word>')
            cleaned_word = xml_response[sensitive_word_start:sensitive_word_end].strip()
        else:
            # 如果找不到标签，返回原始响应
            cleaned_word = ""
        
        # 提取 <reasoning> 标签内容
        reasoning_start = xml_response.find('<reasoning>')
        reasoning_end = xml_response.find('</reasoning>')
        
        if reasoning_start != -1 and reasoning_end != -1:
            reasoning_start += len('<reasoning>')
            reasoning = xml_response[reasoning_start:reasoning_end].strip()
        else:
            reasoning = ""
            
        # 提取 <type> 标签内容
        type_start = xml_response.find('<type>')
        type_end = xml_response.find('</type>')
        
        if type_start != -1 and type_end != -1:
            type_start += len('<type>')
            type = xml_response[type_start:type_end].strip()
        else:
            type = ""
            
        return cleaned_word, reasoning, type
    
    except Exception as e:
        print(f"XML解析出错: {e}")
        # 如果解析失败，返回原始响应
        return "", "", ""

# 处理清洗结果，将LLM返回的答案整理为最终格式
cleaned_results = []
for item in results:
    original_word = item.get("sensitive_word", "")
    raw_llm_response = item.get("answer", "").strip()
    
    # 解析XML格式的LLM响应
    cleaned_word, reasoning, type = parse_xml_response(raw_llm_response)
    cleaned_word = cleaned_word.strip()
    reasoning = reasoning.strip()
    type = type.strip()
    
    # 处理"空"值
    cleaned_word = cleaned_word if (cleaned_word != "" and reasoning != "" and type != "") else ""
    cleaned_word = cleaned_word if cleaned_word != "空" else ""
    
    # 构建结果条目
    result_item = {
        "original_word": original_word,
        "original_label": item.get("label", ""),
        "original_label_name": item.get("label_name", ""),
        "raw_llm_response": raw_llm_response,
        "cleaned_word": cleaned_word,
        "reasoning": reasoning,
        "type": type,
        "action": "删除" if cleaned_word == "" else ("保留" if cleaned_word == original_word else "优化")
    }
    cleaned_results.append(result_item)

# 保存清洗结果
with open("cleaned_sensitive_words.json", 'w', encoding='utf-8') as fp:
    json.dump(cleaned_results, fp, indent=4, ensure_ascii=False)

print("结果已保存到 cleaned_sensitive_words.json")
print(f"共处理 {len(cleaned_results)} 个敏感词")

# 统计处理结果
stats = {"保留": 0, "优化": 0, "删除": 0}
for item in cleaned_results:
    stats[item["action"]] += 1

print(f"处理统计：保留 {stats['保留']} 个，优化 {stats['优化']} 个，删除 {stats['删除']} 个")



import pandas as pd
import json

with open("./labeling/cleaned_sensitive_words.json", "r", encoding="utf-8") as fp:
    json_data = json.load(fp)

# Create a list to store the extracted data
extracted_data = []

# Iterate through each item in the JSON data
for item in json_data:
    if item["cleaned_word"] == "":
        continue
    extracted_data.append({
        "敏感词": item["cleaned_word"],
        "敏感类型": item["type"]
    })

# Create a Pandas DataFrame
df = pd.DataFrame(extracted_data)

# Save the DataFrame to a CSV file
df.to_csv("./labeling/sensitive_words.csv", index=False, encoding="utf-8")

print("数据已成功提取并保存到 sensitive_words.csv 文件。")